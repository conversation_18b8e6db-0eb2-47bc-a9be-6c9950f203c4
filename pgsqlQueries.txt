Functionality : add workflowtype in db (custom or general)
Commit :  
Branch : PRDM-532/custom-workflow
query : ALTER TABLE public.workflows ADD workflow_type varchar NULL;


---------------------------------------------------------------------------

Functionality : add workflowtype in db (custom or general)
Commit :  
Branch : PRDM-532/custom-workflow
query : ALTER TABLE public.operations ADD input_type varchar NULL;
		ALTER TABLE public.operations ADD output_type varchar NULL;
		ALTER TABLE public.operations ADD config json NULL;


		CREATE TYPE operation_enum AS ENUM ('general', 'custom');

		ALTER TABLE operations
		ADD COLUMN operation_type operation_enum;


		ALTER TABLE operations
		ADD COLUMN user_id INT;

		ALTER TABLE operations
		ADD CONSTRAINT fk_operations_user
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

		INSERT INTO operations (name, alias, icon, isActive, user_id, operation_type, input_type, output_type, config)
		VALUES (
		'S3 Input',
		's3-input',
		NULL,
		true,
		NULL,
		'custom',
		NULL,
		NULL,
		$${
			"meta": { "key": "s3-input", "displayName": "S3 Input", "category": "input" ,"description": "Connect to S3 bucket for data input" },
			"ui": { "headerColor": "#f0f9ff" },
			"handles": { "input": false, "output": true },
			"fields": [
			{
				"id": "s3Url",
				"type": "text",
				"label": "Enter the S3 URL:",
				"placeholder": "https://bucket-name.s3.region.amazonaws.com/file.csv",
				"required": true,
				"rules": { "regex": "^https?://", "message": "Enter a valid URL" }
			}
			],
			"connection": { "allowedTargets": ["script", "output"], "maxOutEdges": 5 }
		}$$::jsonb
		);

	INSERT INTO operations (name, alias, icon, isActive, user_id, operation_type, input_type, output_type, config)
		VALUES (
		'Code Input',
		'monaco-input',
		NULL,
		true,
		NULL,
		'custom',
		NULL,
		NULL,
		$${
			"meta": { "key": "monaco-input", "displayName": "Custom Script", "category": "script", "description": "Write custom script logic with code editor" },
			"ui": { "headerColor": "#fef3c7" },
			"handles": { "input": true, "output": true },
			"fields": [
			{
				"id": "monacoCode",
				"type": "code",
				"label": "Code",
				"default": "// Enter your input data here\nconst inputData = [\n  { message: \"Hello from Workflows\" },\n  { message: \"Click the Run (►) button to execute this block\" },\n  { message: \"Drag from the green sequence port to connect a new block\" },\n  { message: \"Click the help (?) button to learn more\" }\n];\n\nreturn inputData;",
				"editorOptions": { "language": "javascript", "height": 250 }
			}
			],
		"connection": { "allowedTargets": ["input", "output"], "maxOutEdges": 5 }
	}$$::jsonb
	);
	
INSERT INTO operations (name, alias, icon, isActive, user_id, operation_type, input_type, output_type, config)
VALUES (
    'Output',
    'output',
    NULL,
    true,
    NULL,
    'custom',
    NULL,
    NULL,
    $${
        "meta": { 
            "key": "output", 
            "displayName": "Output", 
            "category": "output", 
            "description": "Configure output settings for data processing" 
        },
        "ui": { 
            "headerColor": "#f6ffed" 
        },
        "handles": { 
            "input": true, 
            "output": false 
        },
        "fields": [
            {
                "id": "saveToS3",
                "type": "switch",
                "label": "Save to S3",
                "default": false
            },
            {
                "id": "returnDataFrame",
                "type": "switch",
                "label": "Return Data Frame",
                "default": false
            }
        ],
        "connection": { 
            "allowedTargets": [], 
            "maxOutEdges": 0 
        }
    }$$::jsonb
);	

---------------------------------------------------------------------------

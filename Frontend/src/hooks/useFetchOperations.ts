import { useCallback, useEffect, useMemo, useState } from 'react';
import { getRequest } from '../utils/apiHandler';
import { Operation } from '../components/Custom Workflow/CustomNodes/types';

const isValidOperation = (operation: any): operation is Operation => {
  return (
    operation &&
    typeof operation.id === 'number' &&
    typeof operation.name === 'string' &&
    typeof operation.alias === 'string' &&
    operation.config &&
    operation.config.meta &&
    typeof operation.config.meta.key === 'string' &&
    typeof operation.config.meta.displayName === 'string' &&
    typeof operation.config.meta.category === 'string' &&
    ['input', 'script', 'output'].includes(operation.config.meta.category) &&
    Array.isArray(operation.config.fields) &&
    (operation.config.meta.description === undefined || typeof operation.config.meta.description === 'string')
  );
};

// Global cache to store operations by type
const operationsCache = new Map<string, {
  data: Operation[];
  loading: boolean;
  error: string | null;
  timestamp: number;
}>();

// Global promise tracker to prevent concurrent requests
const ongoingRequests = new Map<string, Promise<void>>();

// Subscribers for cache updates
const subscribers = new Map<string, Set<(cache: any) => void>>();

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const fetchOperationsData = async (type: 'general' | 'custom'): Promise<void> => {
  // Check if there's already an ongoing request
  if (ongoingRequests.has(type)) {
    await ongoingRequests.get(type);
    return;
  }

  // Check cache validity
  const cached = operationsCache.get(type);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    return; // Cache is still valid
  }

  // Create and track the request
  const requestPromise = (async () => {
    try {
      // Update loading state
      const loadingState = { data: [], loading: true, error: null, timestamp: Date.now() };
      operationsCache.set(type, loadingState);
      notifySubscribers(type, loadingState);

      const response = await getRequest(`/workflow/operations?operationType=${type}`);
      
      if (response.status === 200) {
        const allOperations = response.data?.data || [];
        const validOperations = allOperations.filter((operation: any) => {
          const valid = isValidOperation(operation);
          if (!valid) console.warn('Invalid operation filtered out:', operation);
          return valid;
        });
        
        const successState = { data: validOperations, loading: false, error: null, timestamp: Date.now() };
        operationsCache.set(type, successState);
        notifySubscribers(type, successState);
      } else {
        const errorState = { data: [], loading: false, error: 'Failed to fetch operations', timestamp: Date.now() };
        operationsCache.set(type, errorState);
        notifySubscribers(type, errorState);
      }
    } catch (err: any) {
      console.error('Error fetching operations:', err);
      const errorState = { data: [], loading: false, error: err?.message || 'Failed to fetch operations', timestamp: Date.now() };
      operationsCache.set(type, errorState);
      notifySubscribers(type, errorState);
    } finally {
      ongoingRequests.delete(type);
    }
  })();

  ongoingRequests.set(type, requestPromise);
  await requestPromise;
};

const notifySubscribers = (type: string, cache: any) => {
  const typeSubscribers = subscribers.get(type);
  if (typeSubscribers) {
    typeSubscribers.forEach(callback => callback(cache));
  }
};

export const useFetchOperations = (type: 'general' | 'custom') => {
  const [operations, setOperations] = useState<Operation[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const updateFromCache = useCallback((cache: any) => {
    setOperations(cache.data);
    setLoading(cache.loading);
    setError(cache.error);
  }, []);

  useEffect(() => {
    // Subscribe to cache updates
    if (!subscribers.has(type)) {
      subscribers.set(type, new Set());
    }
    subscribers.get(type)!.add(updateFromCache);

    // Get initial data from cache or fetch
    const cached = operationsCache.get(type);
    if (cached) {
      updateFromCache(cached);
      // Check if cache is stale and refresh if needed
      if ((Date.now() - cached.timestamp) >= CACHE_DURATION) {
        fetchOperationsData(type);
      }
    } else {
      fetchOperationsData(type);
    }

    // Cleanup subscription on unmount
    return () => {
      subscribers.get(type)?.delete(updateFromCache);
      if (subscribers.get(type)?.size === 0) {
        subscribers.delete(type);
      }
    };
  }, [type, updateFromCache]);

  const refetch = useCallback(async () => {
    // Clear cache and force refetch
    operationsCache.delete(type);
    await fetchOperationsData(type);
  }, [type]);

  const getById = useCallback(
    (id: number) => operations.find(op => op.id === id) || null,
    [operations]
  );

  const byCategory = useMemo(() => {
    return operations.reduce(
      (acc, op) => {
        const category = op.config.meta.category as 'input' | 'script' | 'output';
        if (!acc[category]) acc[category] = [] as Operation[];
        acc[category].push(op);
        return acc;
      },
      { input: [] as Operation[], script: [] as Operation[], output: [] as Operation[] }
    );
  }, [operations]);

  return { operations, loading, error, refetch, getById, byCategory };
};

export type UseFetchOperationsReturn = ReturnType<typeof useFetchOperations>; 
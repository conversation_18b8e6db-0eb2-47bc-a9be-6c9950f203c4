import { useCallback, useRef, useState } from 'react';
import type { Node, Edge } from 'reactflow';
import type { CustomNodeData } from '../components/Custom Workflow/CustomNodes/types';

export type ExecuteStatus = 'idle' | 'loading' | 'success' | 'error';

interface UseExecuteNodeReturn {
  status: ExecuteStatus;
  result: unknown | null;
  error: string | null;
  execute: (node: Node, workflowId: number, nodes: Node[], edges: Edge[]) => Promise<void>;
  cancel: () => void;
}

export const useExecuteNode = (): UseExecuteNodeReturn => {
  const [status, setStatus] = useState<ExecuteStatus>('idle');
  const [result, setResult] = useState<unknown | null>(null);
  const [error, setError] = useState<string | null>(null);
  const controllerRef = useRef<AbortController | null>(null);

  const cancel = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      controllerRef.current = null;
    }
    setStatus('idle');
    setResult(null);
    setError(null);
  }, []);

  const getUpstreamNodeSettings = useCallback((nodeId: string, nodes: Node[], edges: Edge[]): Record<string, any> => {
    const upstreamSettings: Record<string, any> = {};
    const visited = new Set<string>(); // Prevent infinite loops
    
    const collectUpstreamSettings = (currentNodeId: string) => {
      // Prevent infinite loops and avoid processing the same node twice
      if (visited.has(currentNodeId)) return;
      visited.add(currentNodeId);
      
      // Find all edges that connect TO this node (incoming edges)
      const incomingEdges = edges.filter(edge => edge.target === currentNodeId);
      
      // For each incoming edge, get the source node's settings
      incomingEdges.forEach(edge => {
        const sourceNode = nodes.find(n => n.id === edge.source);
        if (sourceNode) {
          const sourceData = sourceNode.data as CustomNodeData;
          
          // Add source node settings with prefix to avoid conflicts
          if (sourceData.settings) {
            Object.keys(sourceData.settings).forEach(key => {
              const prefixedKey = `${sourceData.nodeType}_${key}`;
              upstreamSettings[prefixedKey] = sourceData.settings[key as keyof typeof sourceData.settings] as any;
            });
          }
          
          // Recursively collect settings from this source node's upstream nodes
          collectUpstreamSettings(edge.source);
        }
      });
    };
    
    // Start the recursive collection from the given node
    collectUpstreamSettings(nodeId);
    
    return upstreamSettings;
  }, []);

  const execute = useCallback(async (node: Node, workflowId: number, nodes: Node[], edges: Edge[]) => {
    // Cancel any in-flight request before starting a new one
    if (controllerRef.current) {
      controllerRef.current.abort();
      controllerRef.current = null;
    }

    const data = node.data as CustomNodeData;
    
    // Get settings from connected upstream nodes
    const upstreamSettings = getUpstreamNodeSettings(node.id, nodes, edges);
    
    // Merge upstream settings with current node settings (current node takes precedence)
    const mergedSettings = {
      ...upstreamSettings,
      ...data.settings
    };
    
    const payload = {
      workflow_id: workflowId,
      node_id: node.id,
      type: data.nodeType,
      settings: mergedSettings,
      // Include metadata about upstream connections
      has_upstream_connections: Object.keys(upstreamSettings).length > 0,
      upstream_settings_count: Object.keys(upstreamSettings).length
    };

    const controller = new AbortController();
    controllerRef.current = controller;

    setStatus('loading');
    setResult(null);
    setError(null);

    try {
      const response = await fetch('/execute-custom-flow', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
        signal: controller.signal
      });

      // If aborted, exit silently (a replacement execute likely started)
      if (controller.signal.aborted) return;

      if (!response.ok) {
        const text = await response.text().catch(() => 'Execution failed');
        throw new Error(text || `HTTP ${response.status}`);
      }

      const dataJson = await response.json().catch(() => null);
      setResult(dataJson ?? null);
      setStatus('success');
    } catch (err: any) {
      if (err?.name === 'AbortError') {
        // Swallow aborts triggered by cancel/replace
        return;
      }
      setError(err?.message || 'Execution failed');
      setStatus('error');
    } finally {
      // Clear controller if this request is still the active one
      if (controllerRef.current === controller) {
        controllerRef.current = null;
      }
    }
  }, [getUpstreamNodeSettings]);

  return { status, result, error, execute, cancel };
};

export default useExecuteNode; 
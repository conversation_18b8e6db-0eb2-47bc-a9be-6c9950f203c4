.blends-prediction-table {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
}

.blends-table .ant-table {
  font-size: 12px;
}

.blends-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
  text-align: center;
  padding: 8px 12px;
  border-bottom: 2px solid #e8e8e8;
}

.blends-table .ant-table-tbody > tr > td {
  padding: 8px 12px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.blends-table .ant-table-tbody > tr:hover > td {
  background-color: #f9f9f9;
}

.blends-table .ant-table-tbody > tr:nth-child(even) {
  background-color: #fafafa;
}

.blends-table .ant-table-tbody > tr:nth-child(even):hover > td {
  background-color: #f5f5f5;
}

/* Animation for new rows */
.blends-table .ant-table-tbody > tr {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .blends-prediction-table {
    padding: 12px;
    margin: 12px 0;
  }
  
  .blends-table .ant-table {
    font-size: 11px;
  }
  
  .blends-table .ant-table-thead > tr > th,
  .blends-table .ant-table-tbody > tr > td {
    padding: 6px 8px;
  }
}

/* Loading state */
.blends-table .ant-spin-container {
  min-height: 200px;
}

/* Fixed columns styling */
.blends-table .ant-table-fixed-left {
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
}

.blends-table .ant-table-thead > tr > th.ant-table-cell-fix-left {
  background-color: #fafafa;
}

.blends-table .ant-table-tbody > tr > td.ant-table-cell-fix-left {
  background-color: white;
}

.blends-table .ant-table-tbody > tr:hover > td.ant-table-cell-fix-left {
  background-color: #f9f9f9;
}

.blends-table .ant-table-tbody > tr:nth-child(even) > td.ant-table-cell-fix-left {
  background-color: #fafafa;
}

.blends-table .ant-table-tbody > tr:nth-child(even):hover > td.ant-table-cell-fix-left {
  background-color: #f5f5f5;
}

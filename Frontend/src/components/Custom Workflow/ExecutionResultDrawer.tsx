import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Typography } from 'antd';
import { 
  CloseOutlined, 
  DownOutlined, 
  UpOutlined,
  PlayCircleOutlined 
} from '@ant-design/icons';

const { Text, Title } = Typography;

interface ExecutionResultDrawerProps {
  open: boolean;
  nodeLabel?: string;
  status: 'idle' | 'loading' | 'success' | 'error';
  onClose: () => void;
  onToggle: () => void;
  collapsed: boolean;
  children?: React.ReactNode;
}

const ExecutionResultDrawer: React.FC<ExecutionResultDrawerProps> = ({
  open,
  nodeLabel,
  status,
  onClose,
  onToggle,
  collapsed,
  children
}) => {
  if (!open) return null;

  const getStatusChip = () => {
    const statusConfig = {
      idle: { color: '#6b7280', bg: '#f3f4f6', text: 'Ready' },
      loading: { color: '#3b82f6', bg: '#dbeafe', text: 'Executing...' },
      success: { color: '#10b981', bg: '#d1fae5', text: 'Success' },
      error: { color: '#ef4444', bg: '#fee2e2', text: 'Error' }
    };

    const config = statusConfig[status];
    return (
      <span
        style={{
          color: config.color,
          backgroundColor: config.bg,
          padding: '2px 8px',
          borderRadius: '12px',
          fontSize: '12px',
          fontWeight: 500
        }}
      >
        {config.text}
      </span>
    );
  };

  const renderContent = () => {
    if (!nodeLabel) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center py-8">
          <PlayCircleOutlined 
            style={{ fontSize: '48px', color: '#6b7280', marginBottom: '16px' }} 
          />
          <Title level={4} className="!text-gray-600 !mb-2">
            Execute Node to Fetch Results
          </Title>
          <Text type="secondary">
            Click the execute (▶) button on any node to see results here
          </Text>
        </div>
      );
    }

    return children || (
      <div className="flex items-center justify-center h-full">
        <Text type="secondary">Results will appear here</Text>
      </div>
    );
  };

  return (
    <div
      style={{
        height: collapsed ? '48px' : '30%',
        minHeight: collapsed ? '48px' : '240px',
        transition: 'height 0.3s ease-in-out'
      }}
      className="border-t border-gray-200 bg-white"
    >
      <Card
        size="small"
        className="h-full"
        bodyStyle={{ 
          padding: 0, 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column' 
        }}
      >
        {/* Header */}
        <div 
          className="flex items-center justify-between px-4 py-3 border-b border-gray-100 bg-gray-50 cursor-pointer"
          onClick={onToggle}
        >
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {collapsed ? (
                <UpOutlined style={{ fontSize: '12px', color: '#6b7280' }} />
              ) : (
                <DownOutlined style={{ fontSize: '12px', color: '#6b7280' }} />
              )}
              <Title level={5} className="!mb-0 !text-gray-800">
                Execution Results
              </Title>
            </div>
            {nodeLabel && (
              <div className="flex items-center gap-2">
                <span className="text-gray-400">•</span>
                <Text className="text-sm font-medium text-gray-700">
                  {nodeLabel}
                </Text>
                {getStatusChip()}
              </div>
            )}
          </div>
          
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="text-gray-500 hover:text-gray-700"
          />
        </div>

        {/* Content */}
        {!collapsed && (
          <div className="flex-1" style={{ overflow: status === 'error' ? 'hidden' : 'auto' }}>
            {renderContent()}
          </div>
        )}
      </Card>
    </div>
  );
};

export default ExecutionResultDrawer; 
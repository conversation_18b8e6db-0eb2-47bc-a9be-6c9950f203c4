import React, { useState } from 'react';
import { Button } from 'antd';
import { SaveModal } from '../Modal/workflowModal';

interface CustomFlowActionsProps {
  onSave: (workflowName?: string) => void;
  visible: boolean;
  workflowId: number;
}

const CustomFlowActions: React.FC<CustomFlowActionsProps> = ({ onSave, visible, workflowId }) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  if (!visible) return null;

  const openModalIfNeeded = () => {
    if (workflowId === 0) {
      setIsModalOpen(true);
    } else {
      onSave();
    }
  };

  return (
    <div className="flow-actions flex">
      <Button onClick={openModalIfNeeded} className="save-section btn-primary-new">
        <span className="save-section">Save</span>
      </Button>
      <SaveModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} onSave={(name) => onSave(name)} type={'save'} />
    </div>
  );
};

export default CustomFlowActions;



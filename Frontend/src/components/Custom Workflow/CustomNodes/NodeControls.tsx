import React from 'react';
import { Button, Tooltip } from 'antd';
import { DeleteOutlined, MinusOutlined, PlusOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { NodeControlsProps } from './types';

const NodeControls: React.FC<NodeControlsProps> = ({
  nodeId,
  minimized,
  onDelete,
  onToggleMinimize,
  onExecute,
  variant = 'overlay'
}) => {
  const wrapperClass =
    variant === 'overlay'
      ? 'absolute right-2 top-1/2 -translate-y-1/2 flex flex-row items-center gap-1'
      : 'flex flex-row items-center gap-1';

  const buttonClass = "w-7 h-7 flex items-center justify-center rounded-lg border-0 transition-all duration-200";

  return (
    <div className={wrapperClass}>
      <Tooltip title={minimized ? "Expand" : "Minimize"}>
        <Button
          size="small"
          type="text"
          icon={minimized ? <PlusOutlined /> : <MinusOutlined />}
          onClick={(e) => {
            e.stopPropagation();
            onToggleMinimize(nodeId);
          }}
          className={`${buttonClass} bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800`}
        />
      </Tooltip>
      <Tooltip title="Execute">
        <Button
          size="small"
          type="text"
          icon={<PlayCircleOutlined />}
          onClick={(e) => {
            e.stopPropagation();
            onExecute(nodeId);
          }}
          className={`${buttonClass} bg-blue-100 hover:bg-blue-200 text-blue-600 hover:text-blue-800`}
        />
      </Tooltip>
      <Tooltip title="Delete">
        <Button
          size="small"
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={(e) => {
            e.stopPropagation();
            onDelete(nodeId);
          }}
          className={`${buttonClass} bg-red-100 hover:bg-red-200 text-red-600 hover:text-red-800`}
        />
      </Tooltip>
    </div>
  );
};

export default NodeControls; 
import React from 'react';
import { Switch } from 'antd';
import JsonConfigEditor from './JsonConfigEditor';
import { CustomNodeData } from './types';

interface JsonToggleWrapperProps {
  nodeData: CustomNodeData;
  onToggleJsonConfig: (nodeId: string) => void;
  children: React.ReactNode;
  nodeId: string;
}

const JsonToggleWrapper: React.FC<JsonToggleWrapperProps> = ({ 
  nodeData, 
  onToggleJsonConfig, 
  children,
  nodeId
}) => {
  return (
    <div className="relative group">
      {/* JSON Toggle Switch - hovering in top right corner */}
      <div className="absolute top-[-8px] right-0 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm px-2 py-1">
          <Switch
            size="small"
            checked={nodeData.showJsonConfig}
            onChange={() => onToggleJsonConfig(nodeId)}
          />
        </div>
      </div>
      
      {/* Content based on toggle state */}
      {nodeData.showJsonConfig ? (
        <div>
          <JsonConfigEditor nodeData={nodeData} />
        </div>
      ) : (
        <div>
          {children}
        </div>
      )}
    </div>
  );
};

export default JsonToggleWrapper; 
import React from 'react';
import { Card } from 'antd';
import { Handle, Position } from 'reactflow';
import NodeControls from './NodeControls';
import { BaseNodeProps } from './types';

interface BaseNodeInternalProps extends BaseNodeProps {
  children: React.ReactNode;
  headerColor: string;
  headerIcon: React.ReactNode;
  showInputHandle?: boolean;
  showOutputHandle?: boolean;
  width?: number;
}

const BaseNode: React.FC<BaseNodeInternalProps> = ({
  id,
  data,
  selected,
  children,
  headerColor,
  headerIcon,
  showInputHandle = false,
  showOutputHandle = false,
  width = 280,
  onSettingsChange,
  onDelete,
  onToggleMinimize,
  onExecute
}) => {
  const minimizedHeight = 48;
  const headerHeight = data.minimized ? 60 : 74;
  const handleTopPosition = headerHeight / 2 - 6;

  return (
    <div className="group relative">
      {showInputHandle && (
        <Handle
          id="in"
          type="target"
          position={Position.Left}
          style={{
            left: -8,
            top: handleTopPosition,
            width: 12,
            height: 12,
            background: '#6366f1',
            border: '2px solid white',
            borderRadius: '50%',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            position: 'absolute',
            zIndex: 20
          }}
        />
      )}
      
      {showOutputHandle && (
        <Handle
          id="out"
          type="source"
          position={Position.Right}
          style={{
            right: -8,
            top: handleTopPosition,
            width: 12,
            height: 12,
            background: '#10b981',
            border: '2px solid white',
            borderRadius: '50%',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            position: 'absolute',
            zIndex: 20
          }}
        />
      )}

      <Card
        size="small"
        style={{
          width,
          minHeight: data.minimized ? minimizedHeight : 120,
          border: selected ? '2px solid #3b82f6' : '1px solid #e5e7eb',
          borderRadius: 12,
          boxShadow: selected 
            ? '0 4px 12px rgba(59, 130, 246, 0.15), 0 2px 4px rgba(0, 0, 0, 0.05)' 
            : '0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden',
          background: '#ffffff'
        }}
        bodyStyle={{ padding: 0 }}
      >
        <div 
          style={{ 
            background: headerColor,
            padding: data.minimized ? '12px 16px' : '16px 20px',
            borderBottom: data.minimized ? 'none' : '1px solid #f3f4f6',
            display: 'flex',
            alignItems: 'center',
            gap: 12,
            borderRadius: data.minimized ? '12px' : '12px 12px 0 0'
          }}
        >
          <div className="flex items-center gap-3 flex-1">
            {headerIcon}
            <span className="font-semibold text-sm text-gray-800">{data.label}</span>
          </div>
          {!data.minimized && (
            <NodeControls
              nodeId={id}
              minimized={data.minimized}
              onDelete={onDelete}
              onToggleMinimize={onToggleMinimize}
              onExecute={onExecute}
              variant="inline"
            />
          )}
        </div>
        
        {!data.minimized && (
          <div style={{ padding: '12px', display: 'flex', flexDirection: 'column' }}>
            {children}
          </div>
        )}
      </Card>

      {data.minimized && (
        <NodeControls
          nodeId={id}
          minimized={data.minimized}
          onDelete={onDelete}
          onToggleMinimize={onToggleMinimize}
          onExecute={onExecute}
          variant="overlay"
        />
      )}
    </div>
  );
};

export default BaseNode; 
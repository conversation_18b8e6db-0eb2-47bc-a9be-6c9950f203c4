import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ReactFlow, { 
  Background, 
  Controls, 
  Edge, 
  Node, 
  ReactFlowInstance, 
  addEdge, 
  useEdgesState,
  useNodesState,
  applyNodeChanges,
  NodeChange,
  Connection,
  ReactFlowProvider,
  NodeProps,
  ConnectionMode
} from 'reactflow';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { message, Spin, Button, Typography, Result } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { postRequest, putRequest } from '../../utils/apiHandler';
import CustomFlowActions from './CustomFlowActions';
import ExecutionResultDrawer from './ExecutionResultDrawer';
import { InputNode, ScriptNode, OutputNode } from './CustomNodes';
import { CustomNodeData, InputNodeData, ScriptNodeData, OutputNodeData, NodeType } from './CustomNodes/types';
import { useExecuteNode } from '../../hooks/useExecuteNode';
import 'reactflow/dist/style.css';

const { Text } = Typography;

const InputNodeWrapper = (props: NodeProps) => <InputNode {...props} />;
const ScriptNodeWrapper = (props: NodeProps) => <ScriptNode {...props} />;
const OutputNodeWrapper = (props: NodeProps) => <OutputNode {...props} />;
const nodeTypes = {
  'input-node': InputNodeWrapper,
  'script-node': ScriptNodeWrapper,
  'output-node': OutputNodeWrapper,
};

interface CustomFlowCanvasProps {
  nodes: Node[];
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  onDragOver: (event: React.DragEvent) => void;
  onDrop: (event: React.DragEvent) => void;
  edges?: Edge[];
}

const CustomFlowCanvas: React.FC<CustomFlowCanvasProps> = ({ nodes, setNodes, onDragOver, onDrop, edges: initialEdges = [] }) => {
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  
  // Execution panel state
  const [panelOpen, setPanelOpen] = useState(false);
  const [panelCollapsed, setPanelCollapsed] = useState(false);
  const [activeNodeId, setActiveNodeId] = useState<string | null>(null);
  const [activeNodeLabel, setActiveNodeLabel] = useState<string | null>(null);

  // Centralized execution hook
  const { status: execStatus, result: execResult, error: execError, execute, cancel } = useExecuteNode();
  
  const reactFlowRef = useRef<ReactFlowInstance | null>(null);
  const nodesRef = useRef(nodes);
  const edgesRef = useRef(edges);
  const location = useLocation();
  const navigate = useNavigate();
  const params = new URLSearchParams(location.search);
  const workflowIdParam = params.get('workflowId');
  const workflowId = workflowIdParam ? parseInt(workflowIdParam) : 0;
  const selectSystems = useSelector((state: any) => state?.systems?.systems);

  useEffect(() => {
    if (initialEdges && initialEdges.length > 0) {
      setEdges(initialEdges);
    }
  }, [initialEdges, setEdges]);

  useEffect(() => {
    nodesRef.current = nodes;
  }, [nodes]);

  useEffect(() => {
    edgesRef.current = edges;
  }, [edges]);

  const onInit = useCallback((instance: ReactFlowInstance) => {
    reactFlowRef.current = instance;
    instance.fitView();
  }, []);

  const onNodesChange = useCallback((changes: NodeChange[]) => {
    setNodes((prev) => applyNodeChanges(changes, prev));
  }, [setNodes]);

  const isValidConnection = useCallback((connection: Connection) => {
    if (!connection.source || !connection.target) return false;
    const sourceNode = nodesRef.current.find(node => node.id === connection.source);
    const targetNode = nodesRef.current.find(node => node.id === connection.target);
    if (!sourceNode || !targetNode) return false;
    const sourceData = sourceNode.data as CustomNodeData;
    const targetData = targetNode.data as CustomNodeData;
    if (sourceData.category === 'input' && targetData.category === 'input') return false;
    return true;
  }, []);

  const onConnect = useCallback((params: Connection) => {
    if (isValidConnection(params)) {
      setEdges((eds) => addEdge(params, eds));
    }
  }, [setEdges, isValidConnection]);

  const handleNodeSettingsChange = useCallback((nodeId: string, newSettings: Record<string, any>) => {
    setNodes((prevNodes) => 
      prevNodes.map((node) => 
        node.id === nodeId 
          ? { 
              ...node, 
              data: { 
                ...node.data, 
                settings: { ...(node.data as CustomNodeData).settings, ...newSettings } 
              } 
            }
          : node
      )
    );
  }, [setNodes]);

  const handleDeleteNode = useCallback((nodeId: string) => {
    setNodes(prevNodes => prevNodes.filter(node => node.id !== nodeId));
    setEdges(prevEdges => prevEdges.filter(edge => 
      edge.source !== nodeId && edge.target !== nodeId
    ));
    
    // Close panel if the deleted node was being executed
    setActiveNodeId(prevActiveNodeId => {
      if (prevActiveNodeId === nodeId) {
        setActiveNodeLabel(null);
        setPanelOpen(false);
        cancel();
        return null;
      }
      return prevActiveNodeId;
    });
  }, [setNodes, setEdges, cancel]);

  const handleToggleMinimize = useCallback((nodeId: string) => {
    setNodes((prevNodes) => 
      prevNodes.map((node) => 
        node.id === nodeId 
          ? { 
              ...node, 
              data: { 
                ...node.data, 
                minimized: !(node.data as CustomNodeData).minimized 
              } 
            }
          : node
      )
    );
  }, [setNodes]);

  const handleToggleJsonConfig = useCallback((nodeId: string) => {
    setNodes((prevNodes) => 
      prevNodes.map((node) => 
        node.id === nodeId 
          ? { 
              ...node, 
              data: { 
                ...node.data, 
                showJsonConfig: !(node.data as CustomNodeData).showJsonConfig 
              } 
            }
          : node
      )
    );
  }, [setNodes]);

  const handleExecuteNode = useCallback((nodeId: string) => {
    // Open the panel and set context
    setPanelOpen(true);
    setPanelCollapsed(false);
    setActiveNodeId(nodeId);

    const node = nodesRef.current.find(n => n.id === nodeId);
    const label = node ? (node.data as CustomNodeData).label : null;
    setActiveNodeLabel(label);

    if (node) {
      execute(node, workflowId, nodesRef.current, edgesRef.current);
    }
  }, [execute, workflowId]);

  const handlePanelClose = useCallback(() => {
    setPanelOpen(false);
    setActiveNodeId(null);
    setActiveNodeLabel(null);
    cancel();
  }, [cancel]);

  const handlePanelToggle = useCallback(() => {
    setPanelCollapsed(prev => !prev);
  }, []);

  const onNodesDeleteCallback = useCallback((deletedNodes: Node[]) => {
    deletedNodes.forEach(node => handleDeleteNode(node.id));
  }, [handleDeleteNode]);

  const createNodeData = (operationConfig: any, operationId: number): CustomNodeData => {
    const { meta, fields } = operationConfig;
    
    // Initialize settings from field defaults
    const settings: Record<string, any> = {};
    fields.forEach((field: any) => {
      if (field.default !== undefined) {
        settings[field.id] = field.default;
      }
    });

    const baseData = {
      label: meta.displayName,
      nodeType: meta.key,
      category: meta.category,
      minimized: true,
      settings,
      operationId
    };

    return baseData as CustomNodeData;
  };

  const getReactFlowNodeType = (category: string): string => {
    switch (category) {
      case 'input':
        return 'input-node';
      case 'script':
        return 'script-node';
      case 'output':
        return 'output-node';
      default:
        return 'input-node';
    }
  };

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    try {
      const data = JSON.parse(event.dataTransfer.getData('application/reactflow'));
      if (data?.type !== 'custom-workflow-node' || !data?.config) return;

      // Validate operation config structure
      if (!data.config?.meta?.key || !data.config?.meta?.category || !Array.isArray(data.config?.fields)) {
        console.error('Invalid operation config in drop data:', data);
        return;
      }

      const position = reactFlowRef.current?.project({ x: event.clientX, y: event.clientY }) || { x: 100, y: 100 };
      const id = `${data.alias}-${Date.now()}`;
      
      const nodeData = createNodeData(data.config, data.operationId);
      const reactFlowNodeType = getReactFlowNodeType(data.category);

      const newNode: Node<CustomNodeData> = {
        id,
        type: reactFlowNodeType,
        position,
        data: nodeData
      };
      
      setNodes((prev) => [...prev, newNode]);
    } catch (error) {
      console.error('Error handling drop:', error);
    }
  }, [setNodes]);

  const buildPayload = useCallback((workflowName?: string) => {
    return {
      workflow: {
        user_id: 1,
        name: workflowName || 'Custom Workflow',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        workflow_type: 'custom',
      },
      workflowStructure: {
        hierarchy: {
          nodes: nodes.map(node => ({
            ...node,
            data: {
              ...node.data,
              settings: (node.data as CustomNodeData).settings
            }
          })),
          edges,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      workflowComponents: nodes.map((node) => ({
        component: node.id,
        type: (node.data as CustomNodeData).nodeType,
        settings: (node.data as CustomNodeData).settings,
        operationId: (node.data as CustomNodeData).operationId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })),
      systems: {
        names: selectSystems?.[0]?.systems || [],
      },
    };
  }, [nodes, edges, selectSystems]);

  const handleSave = useCallback(async (workflowName?: string) => {
    try {
      const payload = buildPayload(workflowName);
      if (!workflowId || workflowId === 0) {
        const response = await postRequest('/workflow', payload);
        if (response.status === 201) {
          message.success('Workflow saved successfully');
          const id = response.data?.data?.workflow?.id;
          if (id) navigate(`/?tab=insight&workflowId=${id}&custom=true`);
        }
      } else {
        const response = await putRequest(`/workflow/${workflowId}`, payload);
        if (response.status === 200) {
          message.success('Workflow updated successfully');
          navigate(`/?tab=insight&workflowId=${workflowId}&custom=true`);
        }
      }
    } catch (error) {
      message.error('Failed to save workflow');
    }
  }, [buildPayload, workflowId, navigate]);

  const nodeTypesWithCallbacks = useMemo(() => {
    const createNodeWrapper = (WrappedComponent: React.ComponentType<NodeProps>) => {
      return (props: NodeProps) => (
        <WrappedComponent 
          {...props}
          data={{
            ...props.data,
            onSettingsChange: handleNodeSettingsChange,
            onDelete: handleDeleteNode,
            onToggleMinimize: handleToggleMinimize,
            onExecute: handleExecuteNode,
            onToggleJsonConfig: handleToggleJsonConfig
          }}
        />
      );
    };

    return {
      'input-node': createNodeWrapper(InputNodeWrapper),
      'script-node': createNodeWrapper(ScriptNodeWrapper),
      'output-node': createNodeWrapper(OutputNodeWrapper),
    };
  }, [handleNodeSettingsChange, handleDeleteNode, handleToggleMinimize, handleExecuteNode, handleToggleJsonConfig]);

  // Panel content for Phase 2
  const renderPanelBody = () => {
    if (execStatus === 'loading') {
      return (
        <div className="flex items-center justify-center h-full">
          <Spin size="large" />
        </div>
      );
    }

    if (execStatus === 'error') {
      return (
        <div className="h-full flex items-center justify-center p-4">
          <Result
            status={"error"}
            icon={null}
            title="Execution failed"
            subTitle="Something went wrong while executing this node. Please try again."
            extra={[
              <Button key="retry" type="primary" icon={<ReloadOutlined />} onClick={() => {
                if (!activeNodeId) return;
                const node = nodesRef.current.find(n => n.id === activeNodeId);
                if (node) execute(node, workflowId, nodesRef.current, edgesRef.current);
              }}>
                Retry
              </Button>
            ]}
          />
        </div>
      );
    }

    if (execStatus === 'success') {
      return (
        <div className="h-full overflow-auto p-3">
          <pre className="text-xs bg-gray-50 p-3 rounded border border-gray-200 whitespace-pre-wrap break-all">
            {JSON.stringify(execResult, null, 2)}
          </pre>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center h-full">
        <Text type="secondary">Results will appear here</Text>
      </div>
    );
  };

  return (
    <div 
      className="w-full h-full flex flex-col" 
      onDragOver={onDragOver} 
      onDrop={handleDrop}
    >
      <CustomFlowActions
        visible={edges.length > 0 || nodes.length > 0}
        onSave={handleSave}
        workflowId={workflowId}
      />
      
      {/* ReactFlow Area */}
      <div 
        className="flex-1"
        style={{ 
          height: panelOpen ? '70%' : '100%',
          minHeight: panelOpen ? '200px' : 'auto',
          transition: 'height 0.3s ease-in-out'
        }}
      >
        <ReactFlowProvider>
          <ReactFlow
            onInit={onInit}
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodesDelete={onNodesDeleteCallback}
            nodeTypes={nodeTypesWithCallbacks}
            connectionMode={ConnectionMode.Strict}
            isValidConnection={isValidConnection}
            fitView
            nodesDraggable={true}
            nodesConnectable={true}
            elementsSelectable={true}
            selectNodesOnDrag={false}
          >
            <Controls />
            <Background />
          </ReactFlow>
        </ReactFlowProvider>
      </div>

      {/* Execution Result Drawer */}
      <ExecutionResultDrawer
        open={panelOpen}
        nodeLabel={activeNodeLabel || undefined}
        status={execStatus}
        onClose={handlePanelClose}
        onToggle={handlePanelToggle}
        collapsed={panelCollapsed}
      >
        {renderPanelBody()}
      </ExecutionResultDrawer>
    </div>
  );
};

export default CustomFlowCanvas;



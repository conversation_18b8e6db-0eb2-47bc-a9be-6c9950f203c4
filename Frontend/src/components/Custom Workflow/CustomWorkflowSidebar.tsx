import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON>ton, Typography, Divide<PERSON>, <PERSON><PERSON><PERSON>, Spin } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import arrowleft from '../../img/arrowleftblue.svg';
import { CodeOutlined, SendOutlined, CloudUploadOutlined } from '@ant-design/icons';
import { Operation, NodeCategory } from './CustomNodes/types';
import { useFetchOperations } from '../../hooks/useFetchOperations';

const { Title, Paragraph } = Typography;

const CustomWorkflowSidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { operations, loading, error, byCategory } = useFetchOperations('custom');

  const handleBackClick = () => {
    navigate('/?tab=insight&workflowId=0');
  };

  const handleDragStart = (e: React.DragEvent, operation: Operation) => {
    if (!operation.isActive) {
      e.preventDefault();
      return;
    }
    
    if (!operation?.config?.meta) {
      console.error('Operation missing config.meta:', operation);
      e.preventDefault();
      return;
    }
    
    const payload = {
      operationId: operation.id,
      name: operation.name,
      alias: operation.alias,
      nodeType: operation.config.meta.key,
      category: operation.config.meta.category,
      type: 'custom-workflow-node',
      config: operation.config
    };
    
    e.dataTransfer.setData('application/reactflow', JSON.stringify(payload));
  };

  const getIconForOperation = (operation: Operation) => {
    if (operation.icon) {
      return (
        <img 
          src={operation.icon} 
          alt={operation.name}
          style={{ width: '16px', height: '16px' }}
          onError={(e) => {
            console.warn(`Failed to load icon: ${operation.icon}`);
            e.currentTarget.style.display = 'none';
          }}
        />
      );
    }
    
    // Fallback icons based on category
    const category = operation.config?.meta?.category;
    switch (category) {
      case 'input':
        return <CloudUploadOutlined style={{ color: '#3b82f6', fontSize: '16px' }} />;
      case 'script':
        return <CodeOutlined style={{ color: '#f59e0b', fontSize: '16px' }} />;
      case 'output':
        return <SendOutlined style={{ color: '#1890ff', fontSize: '16px' }} />;
      default:
        return <CodeOutlined style={{ color: '#666666', fontSize: '16px' }} />;
    }
  };

  const operationsByCategory = useMemo(() => {
    const grouped: Record<NodeCategory, Operation[]> = {
      input: [],
      script: [],
      output: []
    };

    operations.forEach(operation => {
      if (!operation?.config?.meta?.category) {
        console.warn('Operation missing config.meta.category:', operation);
        return;
      }
      const category = operation.config.meta.category;
      if (grouped[category]) {
        grouped[category].push(operation);
      } else {
        console.warn('Unknown category for operation:', category, operation);
      }
    });

    return grouped;
  }, [operations]);

  const renderNodeSection = (title: string, operations: Operation[], categoryKey: NodeCategory) => (
    <div key={categoryKey} className="mb-6">
      <Title level={5} className="heading-bold mb-3 text-gray-700">
        {title}
      </Title>
      <div className="available-data data-source">
        <ul>
          {operations.map((operation) => {
            const isDisabled = !operation.isActive;
            const listItemClass = `cursor-pointer transition-colors ${
              isDisabled
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-gray-50'
            }`;

            const content = (
              <li
                key={operation.id}
                className={listItemClass}
                draggable={!isDisabled}
                onDragStart={(e) => handleDragStart(e, operation)}
              >
                <div className="flex items-center gap-3 p-2 rounded">
                  <span className="flex-shrink-0">
                    {getIconForOperation(operation)}
                  </span>
                  <div className="flex-1 min-w-0">
                    <Paragraph className="!leading-none !mb-1 font-medium">
                      {operation.config.meta.displayName}
                    </Paragraph>
                    <Paragraph className="!leading-none !text-xs !text-gray-500 !mb-0">
                      {operation.config.meta.description || operation.name}
                    </Paragraph>
                  </div>
                </div>
              </li>
            );

            return isDisabled ? (
              <Tooltip key={operation.id} title="Coming soon">
                {content}
              </Tooltip>
            ) : content;
          })}
        </ul>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex font-medium items-center line-clamp-1 gap-2 !text-sm !text-[#252963] mb-4 cursor-pointer" onClick={handleBackClick}>
          <Button type="text" className="p-0 !bg-transparent">
            <img src={arrowleft} alt="Back" />
          </Button>
          <span>Back</span>
        </div>

        <div className="flex-1 flex items-center justify-center">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex font-medium items-center line-clamp-1 gap-2 !text-sm !text-[#252963] mb-4 cursor-pointer" onClick={handleBackClick}>
          <Button type="text" className="p-0 !bg-transparent">
            <img src={arrowleft} alt="Back" />
          </Button>
          <span>Back</span>
        </div>

        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Paragraph type="danger">{error}</Paragraph>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex font-medium items-center line-clamp-1 gap-2 !text-sm !text-[#252963] mb-4 cursor-pointer" onClick={handleBackClick}>
        <Button type="text" className="p-0 !bg-transparent">
          <img src={arrowleft} alt="Back" />
        </Button>
        <span>Back</span>
      </div>

      <div className="flex-1 overflow-y-auto">
        <Title level={4} className="heading-bold mb-4">
          Workflow Nodes
        </Title>

        {renderNodeSection("Input Nodes", operationsByCategory.input, "input")}

        <Divider className="my-4" />

        {renderNodeSection("Script Nodes", operationsByCategory.script, "script")}

        <Divider className="my-4" />

        {renderNodeSection("Output Nodes", operationsByCategory.output, "output")}
      </div>
    </div>
  );
};

export default CustomWorkflowSidebar;



import React, { useState, useCallback, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import { 
  PLCComponentType, 
  PLCGridItemData, 
  PLCLayout, 
  PLCLayoutItem, 
  PLCGridLayoutProps,
  PLC_GRID_CONFIG,
  PLC_PANEL_SIZES
} from './types/PLCTypes';
import PLCGridItem from './PLCGridItem';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import '../ExplorationStyles.css';

const ReactGridLayout = WidthProvider(RGL);

const PLCGridLayout = forwardRef<{getLayout: () => PLCLayout, getItems: () => PLCGridItemData[]}, PLCGridLayoutProps>(({
  items,
  layout,
  onLayoutChange,
  onItemsChange,
  onDrop,
  onPanelRemove,
  onOpenConfiguration,
  onCreatePhaseIdentification,
  phaseComparisonLoading,
  batchComparisonLoading,
  phaseIdentificationLoading,
  qualityLookup
}, ref) => {
  const [nextId, setNextId] = useState(1);
  const gridContainerRef = useRef<HTMLDivElement>(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    getLayout: () => layout,
    getItems: () => items
  }));



  // Handle layout change from react-grid-layout
  const handleLayoutChange = useCallback((newLayout: any[]) => {
    const plcLayout: PLCLayout = newLayout.map(item => ({
      i: item.i,
      x: item.x,
      y: item.y,
      w: item.w,
      h: item.h,
      minW: item.minW,
      minH: item.minH,
      maxW: item.maxW,
      maxH: item.maxH,
      static: item.static,
      isDraggable: item.isDraggable,
      isResizable: item.isResizable,
    }));
    onLayoutChange(plcLayout);
  }, [onLayoutChange]);

  // Handle drop on grid (from react-grid-layout)
  const handleDrop = useCallback((layout: any[], layoutItem: any, event: any) => {
    console.log('🎯 ReactGridLayout drop:', { layout, layoutItem, event });
    
    const componentType = event.dataTransfer?.getData('component') as PLCComponentType;
    if (!componentType) {
      return;
    }
    
    const x = layoutItem.x || 0;
    const y = layoutItem.y || 0;
    
    onDrop(componentType, x, y);
  }, [onDrop]);



  // Handle panel removal
  const handlePanelRemove = useCallback((panelId: string) => {
    const updatedItems = items.filter(item => item.id !== panelId);
    const updatedLayout = layout.filter(item => item.i !== panelId);
    
    onItemsChange(updatedItems);
    onLayoutChange(updatedLayout);
    onPanelRemove(panelId);
  }, [items, layout, onItemsChange, onLayoutChange, onPanelRemove]);



  // Helper function to render drop indicator (same as batch exploration)
  const renderDropIndicator = () => {
    if (items.length === 0) {
      return (
        <div className="drop-indicator">
          Drag components from the sidebar and drop here
        </div>
      );
    }
    return null;
  };

  // Empty state when no panels
  if (items.length === 0) {
    return (
      <div
        className="plc-grid-container"
        ref={gridContainerRef}
        style={{ height: '100%', position: 'relative' }}
      >
        {renderDropIndicator()}
        <ReactGridLayout
          className="plc-layout"
          layout={[]}
          cols={PLC_GRID_CONFIG.COLS}
          rowHeight={PLC_GRID_CONFIG.ROW_HEIGHT}
          margin={PLC_GRID_CONFIG.MARGIN}
          containerPadding={PLC_GRID_CONFIG.CONTAINER_PADDING}
          onLayoutChange={handleLayoutChange}
          onDrop={handleDrop}
          isDroppable={true}
          droppingItem={{ 
            i: '__dropping-elem__', 
            w: PLC_PANEL_SIZES.DEFAULT.w, 
            h: PLC_PANEL_SIZES.DEFAULT.h 
          }}
          preventCollision={false}
          compactType="vertical"
          useCSSTransforms={true}
          allowOverlap={false}
          draggableHandle=".plc-drag-handle"
          resizeHandles={['s', 'e', 'n', 'w', 'se', 'sw', 'ne', 'nw']}
        />
      </div>
    );
  }

  return (
    <div
      className="plc-grid-container"
      ref={gridContainerRef}
      style={{ height: '100%', position: 'relative' }}
    >
      <ReactGridLayout
        className="plc-layout"
        layout={layout}
        cols={PLC_GRID_CONFIG.COLS}
        rowHeight={PLC_GRID_CONFIG.ROW_HEIGHT}
        margin={PLC_GRID_CONFIG.MARGIN}
        containerPadding={PLC_GRID_CONFIG.CONTAINER_PADDING}
        onLayoutChange={handleLayoutChange}
        onDrop={handleDrop}
        isDroppable={true}
        droppingItem={{ 
          i: '__dropping-elem__', 
          w: PLC_PANEL_SIZES.DEFAULT.w, 
          h: PLC_PANEL_SIZES.DEFAULT.h 
        }}
        preventCollision={false}
        compactType="vertical"
        useCSSTransforms={true}
        allowOverlap={false}
        draggableHandle=".plc-drag-handle"
        resizeHandles={['s', 'e', 'n', 'w', 'se', 'sw', 'ne', 'nw']}
      >
        {items.map(item => {
          const itemLayout = layout.find(l => l.i === item.id);
          return (
            <div key={item.id} className="plc-grid-item-wrapper">
              <PLCGridItem
                data={item}
                layout={itemLayout}
                onRemove={handlePanelRemove}
                onOpenConfiguration={onOpenConfiguration}
                onConfigurationChange={(panelId, config) => {
                  // Handle configuration changes if needed
                }}
                onCreatePhaseIdentification={onCreatePhaseIdentification}
                phaseComparisonLoading={phaseComparisonLoading}
                batchComparisonLoading={batchComparisonLoading}
                phaseIdentificationLoading={phaseIdentificationLoading}
                qualityLookup={qualityLookup}
              />
            </div>
          );
        })}
      </ReactGridLayout>
    </div>
  );
});

PLCGridLayout.displayName = 'PLCGridLayout';

export default PLCGridLayout;

import React from 'react';
import ReactDOM from 'react-dom';
import { CloseOutlined, SettingOutlined } from '@ant-design/icons';
import './FullScreenModal.css';

interface FullScreenModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  onOpenConfiguration?: (id: string, columns?: any) => void;
  data?: any;
}

const FullScreenModal: React.FC<FullScreenModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  onOpenConfiguration,
  data
}) => {
  if (!isOpen) return null;

    // Handle opening configuration drawer
  const handleOpenConfiguration = () => {
    if (onOpenConfiguration) {
      // Get currently displayed columns from the panel component
      // For now, we'll pass undefined and let the panel determine the columns
      onOpenConfiguration(data.id, undefined);
    }
  };

  // Create portal to render modal directly in document body
  return ReactDOM.createPortal(
    <div className="full-screen-overlay">
      <div className="full-screen-content">
        <div className="full-screen-header">
          <h2>{title}</h2>
        <div className="header-buttons">
          <button
            className="close-button"
            title="Panel configuration"
            onClick={handleOpenConfiguration}
            aria-label="Open configuration"
          >
            <SettingOutlined />
          </button>
          <button
            className="close-button"
            onClick={onClose}
            aria-label="Close full screen"
          >
            <CloseOutlined />
          </button>
        </div>
      </div>
        <div className="full-screen-body">
          {children}
        </div>
      </div>
    </div>,
    document.body // Render directly in the document body
  );
};

export default FullScreenModal;
 
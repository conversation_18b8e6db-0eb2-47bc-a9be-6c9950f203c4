import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ColumnSelection, DateFilter } from '../../types';
import { PanelFilter } from '../../FilterTypes';
import { Spin, Empty, Card, Typography, Row, Col, Statistic } from 'antd';
import ReactECharts from 'echarts-for-react';

const { Title, Text } = Typography;

interface ProcessCapabilityReportPanelProps {
  data: any;
  filteredData?: any;
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string) => void;
  isFullScreen?: boolean;
}

const ProcessCapabilityReportPanel: React.FC<ProcessCapabilityReportPanelProps> = ({
  data,
  filteredData,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  panelFilters = [],
  onZoomSelection,
  isFullScreen = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);

  // Monitor container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setContainerSize({ width: clientWidth, height: clientHeight });
        setTimeout(() => setIsChartReady(true), 100);
      }
    };

    updateSize();
    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0) {
          setContainerSize({ width, height });
          setTimeout(() => setIsChartReady(true), 100);
        }
      }
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => resizeObserver.disconnect();
  }, [isFullScreen]);

  // Process capability data from backend
  const capabilityData = useMemo(() => {
    if (!filteredData || !filteredData.columnOptions) return {};
    return filteredData.columnOptions;
  }, [filteredData]);

  // Check for configuration errors
  const hasConfigError = filteredData?.metadata?.error?.includes('No target variable selected');
  const hasLSLUSLError = filteredData?.metadata?.error?.includes('Please configure valid LSL and USL');
  const hasDataError = filteredData?.metadata?.error?.includes('Insufficient data');

  // Handle loading state
  if (isLoading) {
    return (
      <div className="process-capability-panel h-full flex items-center justify-center">
        <Spin size="large" tip="Loading process capability data..." />
      </div>
    );
  }

  // Handle configuration errors
  if (hasConfigError) {
    return (
      <div className="process-capability-panel h-full flex items-center justify-center">
        <div className="text-center">
          <Empty
            description={
              <div>
                <p className="text-gray-500 mb-2">No target variable selected</p>
                <p className="text-sm text-gray-400">Please configure a target variable in panel settings to generate process capability report</p>
              </div>
            }
          />
        </div>
      </div>
    );
  }

  // Handle LSL/USL configuration errors
  if (hasLSLUSLError) {
    return (
      <div className="process-capability-panel h-full flex items-center justify-center">
        <div className="text-center">
          <Empty
            description={
              <div>
                <p className="text-gray-500 mb-2">Configuration required</p>
                <p className="text-sm text-gray-400">Please configure LSL and USL values in panel settings</p>
              </div>
            }
          />
        </div>
      </div>
    );
  }

  // Handle data errors
  if (hasDataError) {
    return (
      <div className="process-capability-panel h-full flex items-center justify-center">
        <div className="text-center">
          <Empty
            description={
              <div>
                <p className="text-gray-500 mb-2">Insufficient data</p>
                <p className="text-sm text-gray-400">{filteredData?.metadata?.error}</p>
              </div>
            }
          />
        </div>
      </div>
    );
  }

  // Handle empty data state
  if (!data && !filteredData) {
    return (
      <div className="process-capability-panel h-full flex items-center justify-center">
        <Empty description="No data available for process capability analysis" />
      </div>
    );
  }

  // Check if we have capability data
  const targetVariables = Object.keys(capabilityData);
  const hasData = targetVariables.length > 0;

  if (!hasData) {
    return (
      <div className="process-capability-panel h-full flex items-center justify-center">
        <Empty description="No process capability data available" />
      </div>
    );
  }

  // Get the first (and typically only) target variable data
  const targetVariable = targetVariables[0];
  const capabilityInfo = capabilityData[targetVariable];
  const statistics = capabilityInfo?.statistics;
  const echartsOption = capabilityInfo?.echartsOption;

  return (
    <div className="process-capability-panel h-full w-full" ref={containerRef}>
      {isFullScreen ? (
        <div className="h-full flex flex-col">
          {/* Chart Section */}
          <div className="flex-1 p-4">
            {containerSize.width > 0 && containerSize.height > 0 && isChartReady && echartsOption ? (
              <ReactECharts
                key={`capability-${targetVariable}`}
                option={{
                  ...echartsOption,
                  title: undefined
                }}
                style={{
                  width: '100%',
                  height: '100%'
                }}
                opts={{ renderer: 'canvas' }}
              />
            ) : (
              <div style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Spin size="large" tip="Loading chart..." />
              </div>
            )}
          </div>

          {/* Statistics Section */}
          {statistics && (
            <div className="mt-4 px-4 pb-4" style={{ height: '300px' }}>
              <Card
                title={`Process Capability Statistics - ${targetVariable}`}
                size="small"
                style={{ height: '100%' }}
                bodyStyle={{ padding: '12px', height: 'calc(100% - 40px)' }}
              >
                <Row gutter={[16, 8]}>
                  {/* Basic Statistics */}
                  <Col span={12} sm={6}>
                    <Statistic
                      title="Sample Size"
                      value={statistics.sampleSize}
                      precision={0}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={12} sm={6}>
                    <Statistic
                      title="Mean"
                      value={statistics.mean}
                      precision={4}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={12} sm={6}>
                    <Statistic
                      title="Std (Within)"
                      value={statistics.stdWithin}
                      precision={4}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={12} sm={6}>
                    <Statistic
                      title="Std (Overall)"
                      value={statistics.stdOverall}
                      precision={4}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>

                  {/* Specification Limits */}
                  <Col span={12} sm={6}>
                    <Statistic
                      title="LSL"
                      value={statistics.LSL}
                      precision={3}
                      valueStyle={{ fontSize: '14px', color: '#ff7875' }}
                    />
                  </Col>
                  <Col span={12} sm={6}>
                    <Statistic
                      title="USL"
                      value={statistics.USL}
                      precision={3}
                      valueStyle={{ fontSize: '14px', color: '#ff7875' }}
                    />
                  </Col>

                  {/* Capability Indices */}
                  <Col span={12} sm={6}>
                    <Statistic
                      title="Cp (Within)"
                      value={statistics.Cp}
                      precision={3}
                      valueStyle={{ 
                        fontSize: '14px', 
                        color: statistics.cpStatus?.color === '#52c41a' ? '#52c41a' : statistics.cpStatus?.color === '#faad14' ? '#faad14' : '#ff7875',
                        fontWeight: 'bold'
                      }}
                    />
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {statistics.cpStatus?.status}
                    </Text>
                  </Col>
                  <Col span={12} sm={6}>
                    <Statistic
                      title="Cpk (Within)"
                      value={statistics.Cpk}
                      precision={3}
                      valueStyle={{ 
                        fontSize: '14px', 
                        color: statistics.cpkStatus?.color === '#52c41a' ? '#52c41a' : statistics.cpkStatus?.color === '#faad14' ? '#faad14' : '#ff7875',
                        fontWeight: 'bold'
                      }}
                    />
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {statistics.cpkStatus?.status}
                    </Text>
                  </Col>
                  <Col span={12} sm={6}>
                    <Statistic
                      title="Pp (Overall)"
                      value={statistics.Pp}
                      precision={3}
                      valueStyle={{ 
                        fontSize: '14px', 
                        color: statistics.ppStatus?.color === '#52c41a' ? '#52c41a' : statistics.ppStatus?.color === '#faad14' ? '#faad14' : '#ff7875',
                        fontWeight: 'bold'
                      }}
                    />
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {statistics.ppStatus?.status}
                    </Text>
                  </Col>
                  <Col span={12} sm={6}>
                    <Statistic
                      title="Ppk (Overall)"
                      value={statistics.Ppk}
                      precision={3}
                      valueStyle={{ 
                        fontSize: '14px', 
                        color: statistics.ppkStatus?.color === '#52c41a' ? '#52c41a' : statistics.ppkStatus?.color === '#faad14' ? '#faad14' : '#ff7875',
                        fontWeight: 'bold'
                      }}
                    />
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {statistics.ppkStatus?.status}
                    </Text>
                  </Col>

                  <Col span={24}>
                    <Text style={{ fontSize: '11px', color: '#8c8c8c' }}>
                      • Cp/Cpk ≥ 1.33: Capable | Cp/Cpk ≥ 1.00: Marginally Capable | Cp/Cpk &lt; 1.00: Not Capable
                    </Text>
                  </Col>
                </Row>
              </Card>
            </div>
          )}
        </div>
      ) : (
        <div className="h-full flex flex-col relative">
          {/* Chart Section - Normal Mode */}
          <div className="flex-1">
            {containerSize.width > 0 && containerSize.height > 0 && isChartReady && echartsOption ? (
              <ReactECharts
                key={`capability-${targetVariable}`}
                option={{
                  ...echartsOption,
                  title: undefined
                }}
                style={{
                  width: '100%',
                  height: '100%'
                }}
                opts={{ renderer: 'canvas' }}
              />
            ) : (
              <div style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Spin size="large" tip="Loading chart..." />
              </div>
            )}
          </div>

          {/* Note Section - Normal Mode */}
          <div className="absolute left-4 right-4 bottom-2 pointer-events-none">
            <Text style={{ fontSize: '12px', color: '#8c8c8c', fontStyle: 'italic' }}>
              💡 To view detailed statistics, open this panel in full screen mode
            </Text>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProcessCapabilityReportPanel; 
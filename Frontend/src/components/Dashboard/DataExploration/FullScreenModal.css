.full-screen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 999; /* Ensure it's above everything else */
  display: flex;
  justify-content: center;
  align-items: center;
  /* Add to document body instead of being contained in parent */
  position: fixed;
}

/* :where(.css-dev-only-do-not-override-apn68).ant-drawer {
    position: fixed;
    inset: 0;
    z-index: 9999;
    pointer-events: none;
    color: rgba(0, 0, 0, 0.88);
} */
.full-screen-content {
  background-color: #fff;
  width: 98%;
  height: 98%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.full-screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.full-screen-header h2 {
  margin: 0;
  font-size: 18px;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
}

.close-button:hover {
  color: #333;
}

.header-buttons {
  display: flex;
  gap: 8px; /* space between buttons */
}

.full-screen-body {
  flex: 1;
  overflow: auto;
  padding: 16px;
  height: calc(100% - 50px); /* Subtract header height */
}

/* Style for panel content in full screen */
.full-screen-panel-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
 
import React, { useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { ComponentType, GridItemData, ColumnSelection, DateFilter } from './types';
import { PanelFilter } from './FilterTypes';
import TimeSeriesPanel from './BatchExploration/panels/TimeSeriesPanel';
import OverviewPanel from './BatchExploration/panels/OverviewPanel';
import HistogramPanel from './BatchExploration/panels/HistogramPanel';
import DataTablePanel from './BatchExploration/panels/DataTablePanel';
import ScatterPlotPanel from './BatchExploration/panels/ScatterPlotPanel';
import XbarRbarPanel from './BatchExploration/panels/XbarRbarPanel';
import ProcessCapabilityReportPanel from './BatchExploration/panels/ProcessCapabilityReportPanel';
import { CloseOutlined, ExpandOutlined, SettingOutlined } from '@ant-design/icons';
import PanelOptionsMenu from './PanelOptionsMenu';
import FullScreenModal from './FullScreenModal';
import PanelConfigDrawer from './BatchExploration/PanelConfigDrawer';

interface GridItemProps {
  file_id: string;
  data: GridItemData;
  fileData: any;
  filteredData?: any; // Pre-filtered data or panel-specific data
  isLoading?: boolean; // Loading state for exploration mode
  errorMessage?: string | null; // Error message for exploration mode
  isExplorationMode?: boolean; // Flag to determine behavior
  onRemove: (id: string) => void;

  // Selection and filter props
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;

  // Panel-specific filters
  panelFilters?: PanelFilter[];
  conditionalFilters?: PanelFilter[];

  // Selection and filter handlers
  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string , data?:any) => void;

  // Filter management handlers
  onAddFilter?: (filter: PanelFilter) => void;
  onRemoveFilter?: (filterId: string, panelId: string) => void;
  onClearAllFilters?: () => void;

  // Panel configuration handler
  onPanelConfigurationChange?: (panelType: ComponentType, config: any) => void;
  currentConfiguration?: any;

  // Layout information for saving
  layout?: { x: number, y: number, w: number, h: number };
}

const GridItem: React.FC<GridItemProps> = ({
  data,
  fileData,
  filteredData,
  isLoading = false,
  errorMessage = null,
  isExplorationMode = false,
  file_id,
  onRemove,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = [],
  conditionalFilters = [],
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
  onAddFilter,
  onRemoveFilter,
  onClearAllFilters,
  onPanelConfigurationChange,
  currentConfiguration,
  layout
}) => {
  // State for panel display
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [showXbarRbarConfig, setShowXbarRbarConfig] = useState(false);
  const [showScatterPlotConfig, setShowScatterPlotConfig] = useState(false);
  const [showProcessCapabilityConfig, setShowProcessCapabilityConfig] = useState(false);
  const panelContentRef = useRef<HTMLDivElement>(null);

  // State for panel active tabs - lifted up to persist across normal/fullscreen modes
  const [timeSeriesActiveKey, setTimeSeriesActiveKey] = useState("0");
  const [histogramActiveKey, setHistogramActiveKey] = useState("0");
  const [scatterPlotActiveKey, setScatterPlotActiveKey] = useState("0");
  const [xbarRbarActiveKey, setXbarRbarActiveKey] = useState("0");


  // Get systems from Redux store
  const systems = useSelector((state: any) => state.systems);

  // Extract default target variable from systems configurations
  const getDefaultTargetVariable = () => {
    if (!systems?.systems || !Array.isArray(systems.systems)) {
      return null; // No fallback, return null if no systems
    }

    const targetVariables = new Set<string>();
    systems.systems.forEach((system: any) => {
      if (system?.config?.[0]?.configurations?.rules && Array.isArray(system.config[0].configurations.rules)) {
        system.config[0].configurations.rules.forEach((rule: any) => {
          if (rule?.field && typeof rule.field === 'string') {
            targetVariables.add(rule.field);
          }
        });
      }
    });

    const distinctVariables = Array.from(targetVariables);
    const defaultVar = distinctVariables.length > 0 ? distinctVariables[0] : null;
    return defaultVar;
  };

  const effectiveColumnSelection = selectedColumns;

  const renderComponent = (isFullScreenMode = false) => {
    // Show error message for exploration mode
    if (isExplorationMode && errorMessage) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-2">⚠️</div>
            <p className="text-red-600 font-medium">Failed to load data</p>
            <p className="text-gray-500 text-sm mt-1">{errorMessage}</p>
          </div>
        </div>
      );
    }

    // Show loading indicator for exploration mode
    if (isExplorationMode && isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-gray-500">
              {filteredData ? 'Loading filtered data...' : 'Loading data...'}
            </p>
          </div>
        </div>
      );
    }

    switch (data.type) {
      case ComponentType.TimeSeriesPanel:
        return (
          <TimeSeriesPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            conditionalFilters={conditionalFilters}
            onZoomSelection={onZoomSelection}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
            onAddFilter={onAddFilter}
            isFullScreen={isFullScreenMode}
            onRemoveFilter={onRemoveFilter ? (filterId: string, panelId: string) => onRemoveFilter(filterId, panelId) : undefined}
            onClearAllFilters={onClearAllFilters}
            activeKey={timeSeriesActiveKey}
            onActiveKeyChange={setTimeSeriesActiveKey}
          />
        );
      case ComponentType.OverviewPanel:
        return (
          <OverviewPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
          />
        );
      case ComponentType.HistogramPanel:
        return (
          <HistogramPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            isLoading={isLoading}
            panelFilters={panelFilters}
            activeKey={histogramActiveKey}
            onActiveKeyChange={setHistogramActiveKey}
          />
        );
      case ComponentType.DataTablePanel:
        return (
          <DataTablePanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            conditionalFilters={conditionalFilters}
            onDateFilterChange={onDateFilterChange}
            onAddFilter={onAddFilter}
            onRemoveFilter={onRemoveFilter ? (filterId: string) => onRemoveFilter(filterId, data.id) : undefined}
          />
        );
      case ComponentType.ScatterPlotPanel:
        return (
          <ScatterPlotPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
            isFullScreen={isFullScreenMode}
            activeKey={scatterPlotActiveKey}
            onActiveKeyChange={setScatterPlotActiveKey}
          />
        );
      case ComponentType.XbarRbarPanel:
        return (
          <XbarRbarPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
            isFullScreen={isFullScreenMode}
            activeKey={xbarRbarActiveKey}
            onActiveKeyChange={setXbarRbarActiveKey}
          />
        );
      case ComponentType.ProcessCapabilityReportPanel:
        return (
          <ProcessCapabilityReportPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
            isFullScreen={isFullScreenMode}
          />
        );
      default:
        return <div>Unknown panel type</div>;
    }
  };

  // Get panel configuration for saving
  const getPanelConfiguration = () => {
    return {
      selectedColumns: effectiveColumnSelection,
      dateFilter: dateFilter,
      panelFilters: panelFilters,
      conditionalFilters: conditionalFilters,
      fileId: file_id
    };
  };

  // Get applied filters for saving
  const getAppliedFilters = () => {
    const filters = {
      selectedColumns: effectiveColumnSelection,
      dateFilter: dateFilter,
      panelFilters: panelFilters,
      conditionalFilters: conditionalFilters,
      valueRangeFilters: [] // Add if you have value range filters
    };

    console.log('GridItem - Getting applied filters for panel:', data.id, filters);
    return filters;
  };

  const handleRemove = () => {
    onRemove(data.id);
  };

  // Handler for XbarRbar configuration changes
  const handleXbarRbarConfigChange = (config: any) => {
    if (onPanelConfigurationChange) {
      onPanelConfigurationChange(ComponentType.XbarRbarPanel, config);
    }
    setShowXbarRbarConfig(false);
  };

  // Handler for ScatterPlot configuration changes
  const handleScatterPlotConfigChange = (config: any) => {
    if (onPanelConfigurationChange) {
      onPanelConfigurationChange(ComponentType.ScatterPlotPanel, config);
    }
    setShowScatterPlotConfig(false);
  };
  const handleProcessCapabilityConfigChange = (config: any) => {
    if (onPanelConfigurationChange) {
      onPanelConfigurationChange(ComponentType.ProcessCapabilityReportPanel, config);
    }
    setShowProcessCapabilityConfig(false);
  };

  const handleOpenConfiguration = (_id?: string) => {
    if (data.type === ComponentType.XbarRbarPanel) {
      setShowXbarRbarConfig(true);
    } else if (data.type === ComponentType.ScatterPlotPanel) {
      setShowScatterPlotConfig(true);
    } else if (data.type === ComponentType.ProcessCapabilityReportPanel) {
      setShowProcessCapabilityConfig(true);
    }
  };

  return (
    <>
      <div className="grid-item">
        <div className="grid-item-header">
          <div className="grid-item-title drag-handle">{data.title}</div>
          <div className="grid-item-controls no-drag">
            {data.type === ComponentType.XbarRbarPanel && (
              <button
                className="grid-item-control"
                title="Panel Settings"
                onClick={() => setShowXbarRbarConfig(true)}
              >
                <SettingOutlined />
              </button>
            )}
            {data.type === ComponentType.ScatterPlotPanel && (
              <button
                className="grid-item-control"
                title="Panel Settings"
                onClick={() => setShowScatterPlotConfig(true)}
              >
                <SettingOutlined />
              </button>
            )}
            {data.type === ComponentType.ProcessCapabilityReportPanel && (
              <button
                className="grid-item-control"
                title="Panel Settings"
                onClick={() => setShowProcessCapabilityConfig(true)}
              >
                <SettingOutlined />
              </button>
            )}
            <button
              className="grid-item-control"
              title="Expand panel"
              onClick={() => setIsFullScreen(true)}
            >
              <ExpandOutlined />
            </button>
            <PanelOptionsMenu
              panelType={data.type}
              panelRef={panelContentRef}
              panelTitle={data.title}
              configuration={getPanelConfiguration()}
              fileId={file_id}
              panelId={data.id}
              layout={layout}
              onRemove={onRemove}
              appliedFilters={getAppliedFilters()}
            />
            <button
              className="grid-item-control"
              title="Remove from view (temporary)"
              onClick={handleRemove}
            >
              <CloseOutlined />
            </button>
          </div>
        </div>
        <div className="grid-item-content no-drag"  ref={panelContentRef}>{renderComponent(false)}</div>
      </div>

      {/* Full Screen Modal */}
      <FullScreenModal
        isOpen={isFullScreen}
        onClose={() => setIsFullScreen(false)}
        title={data.title}
        onOpenConfiguration={() => handleOpenConfiguration(data.id)}
        data={data}
      >
        <div className="full-screen-panel-content" style={{ width: '100%', height: '100%' }}>
          {/* Create a new instance of the component for full screen view */}
          {renderComponent(true)}
        </div>
      </FullScreenModal>

      {/* XbarRbar Configuration Drawer */}
      {data.type === ComponentType.XbarRbarPanel && (
        <PanelConfigDrawer
          isOpen={showXbarRbarConfig}
          onClose={() => setShowXbarRbarConfig(false)}
          panelType={ComponentType.XbarRbarPanel}
          onConfigurationChange={handleXbarRbarConfigChange}
          currentConfiguration={currentConfiguration || { subgroupSize: 5 }} // Use actual configuration or default
        />
      )}

      {/* ScatterPlot Configuration Drawer */}
      {data.type === ComponentType.ScatterPlotPanel && (
        <PanelConfigDrawer
          isOpen={showScatterPlotConfig}
          onClose={() => setShowScatterPlotConfig(false)}
          panelType={ComponentType.ScatterPlotPanel}
          onConfigurationChange={handleScatterPlotConfigChange}
          currentConfiguration={currentConfiguration || { targetVariable: getDefaultTargetVariable() }} // Use actual configuration or dynamic default
        />
      )}

      {/* Process Capability Report Configuration Drawer */}
      {data.type === ComponentType.ProcessCapabilityReportPanel && (
        <PanelConfigDrawer
          isOpen={showProcessCapabilityConfig}
          onClose={() => setShowProcessCapabilityConfig(false)}
          panelType={ComponentType.ProcessCapabilityReportPanel}
          onConfigurationChange={handleProcessCapabilityConfigChange}
          currentConfiguration={currentConfiguration || { subgroupSize: 5, targetVariable: getDefaultTargetVariable(), LSL: undefined, USL: undefined }}
        />
      )}
    </>
  );
};

export default GridItem;

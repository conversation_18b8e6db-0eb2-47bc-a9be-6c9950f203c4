// Define the layout item for react-grid-layout
export interface LayoutItem {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
  static?: boolean;
}

export type Layout = LayoutItem[];

// Define the file data structure
export interface FileData {
  csv_id: string;
  file_name: string;
  file_path: string;
  version: number;
  created_at?: string;
  updated_at?: string;
  data?: any; // For storing the actual data when loaded
}

// Define the component types that can be added to the grid
export enum ComponentType {
  TimeSeriesPanel = 'TimeSeriesPanel',
  OverviewPanel = 'OverviewPanel',
  HistogramPanel = 'HistogramPanel',
  DataTablePanel = 'DataTablePanel',
  ScatterPlotPanel = 'ScatterPlotPanel',
  XbarRbarPanel = 'XbarRbarPanel',
  ProcessCapabilityReportPanel = 'ProcessCapabilityReportPanel',
}

// Define the grid item data structure
export interface GridItemData {
  id: string;
  type: ComponentType;
  title: string;
  config?: any; // For storing panel-specific configuration
}

// Define interfaces for selections and filters
export interface ColumnSelection {
  indices: number[];
  headers: string[];
}

export interface DateFilter {
  startDate: string | null;
  endDate: string | null;
}

// Mock data structure for development
export interface MockData {
  labels: string[];
  values: number[];
}

// Mock file list
export const mockFiles: any[] = [
  {
    csv_id: 'file1',
    name: 'Sales Data 2023.csv',
    data: {
      barGraph: {
        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
        values: [15000, 22000, 18000, 25000],
      },
      table: [
        ['Date', 'Quarter', 'Sales', 'Expenses', 'Profit'],
        ['2023-01-15', 'Q1', 15000, 9000, 6000],
        ['2023-02-20', 'Q1', 16500, 9500, 7000],
        ['2023-03-25', 'Q1', 17800, 10200, 7600],
        ['2023-04-10', 'Q2', 20000, 11000, 9000],
        ['2023-05-15', 'Q2', 22500, 12500, 10000],
        ['2023-06-20', 'Q2', 23500, 13000, 10500],
        ['2023-07-10', 'Q3', 18000, 11000, 7000],
        ['2023-08-15', 'Q3', 17500, 10500, 7000],
        ['2023-09-20', 'Q3', 19000, 11500, 7500],
        ['2023-10-10', 'Q4', 24000, 14000, 10000],
        ['2023-11-15', 'Q4', 25500, 15000, 10500],
        ['2023-12-20', 'Q4', 26000, 15500, 10500],
      ],
      timeSeries: {
        timestamps: [
          '2023-01-15', '2023-02-20', '2023-03-25', '2023-04-10', '2023-05-15', '2023-06-20',
          '2023-07-10', '2023-08-15', '2023-09-20', '2023-10-10', '2023-11-15', '2023-12-20'
        ],
        values: [15000, 16500, 17800, 20000, 22500, 23500, 18000, 17500, 19000, 24000, 25500, 26000],
      },
      statistics: {
        values: [15000, 16500, 17800, 20000, 22500, 23500, 18000, 17500, 19000, 24000, 25500, 26000],
      },
    },
  },
  {
    csv_id: 'file2',
    name: 'Website Traffic 2023.csv',
    data: {
      barGraph: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        values: [45000, 52000, 49000, 60000, 55000, 70000],
      },
      table: [
        ['Date', 'Month', 'Visitors', 'Page Views', 'Bounce Rate'],
        ['2023-01-05', 'Jan', 45000, 120000, '35%'],
        ['2023-01-15', 'Jan', 47000, 125000, '34%'],
        ['2023-01-25', 'Jan', 48000, 130000, '33%'],
        ['2023-02-05', 'Feb', 50000, 140000, '33%'],
        ['2023-02-15', 'Feb', 52000, 150000, '32%'],
        ['2023-02-25', 'Feb', 54000, 155000, '31%'],
        ['2023-03-05', 'Mar', 48000, 130000, '35%'],
        ['2023-03-15', 'Mar', 49000, 135000, '34%'],
        ['2023-03-25', 'Mar', 50000, 140000, '33%'],
        ['2023-04-05', 'Apr', 58000, 170000, '31%'],
        ['2023-04-15', 'Apr', 60000, 180000, '30%'],
        ['2023-04-25', 'Apr', 62000, 185000, '29%'],
        ['2023-05-05', 'May', 53000, 160000, '32%'],
        ['2023-05-15', 'May', 55000, 165000, '31%'],
        ['2023-05-25', 'May', 57000, 170000, '30%'],
        ['2023-06-05', 'Jun', 68000, 200000, '29%'],
        ['2023-06-15', 'Jun', 70000, 210000, '28%'],
        ['2023-06-25', 'Jun', 72000, 215000, '27%'],
      ],
      timeSeries: {
        timestamps: [
          '2023-01-05', '2023-01-15', '2023-01-25', '2023-02-05', '2023-02-15', '2023-02-25',
          '2023-03-05', '2023-03-15', '2023-03-25', '2023-04-05', '2023-04-15', '2023-04-25',
          '2023-05-05', '2023-05-15', '2023-05-25', '2023-06-05', '2023-06-15', '2023-06-25'
        ],
        values: [45000, 47000, 48000, 50000, 52000, 54000, 48000, 49000, 50000, 58000, 60000, 62000, 53000, 55000, 57000, 68000, 70000, 72000],
      },
      statistics: {
        values: [45000, 47000, 48000, 50000, 52000, 54000, 48000, 49000, 50000, 58000, 60000, 62000, 53000, 55000, 57000, 68000, 70000, 72000],
      },
    },
  },
  {
    csv_id: 'file3',
    name: 'Product Performance.csv',
    data: {
      barGraph: {
        labels: ['Product A', 'Product B', 'Product C', 'Product D', 'Product E'],
        values: [12000, 19000, 15000, 21000, 16000],
      },
      table: [
        ['Date', 'Product', 'Units Sold', 'Revenue', 'Cost', 'Profit'],
        ['2023-01-10', 'Product A', 1200, 12000, 7000, 5000],
        ['2023-01-20', 'Product B', 1900, 19000, 10000, 9000],
        ['2023-02-05', 'Product C', 1500, 15000, 8000, 7000],
        ['2023-02-15', 'Product D', 2100, 21000, 12000, 9000],
        ['2023-03-01', 'Product E', 1600, 16000, 9000, 7000],
        ['2023-03-15', 'Product A', 1300, 13000, 7500, 5500],
        ['2023-04-01', 'Product B', 2000, 20000, 11000, 9000],
        ['2023-04-15', 'Product C', 1600, 16000, 8500, 7500],
        ['2023-05-01', 'Product D', 2200, 22000, 12500, 9500],
        ['2023-05-15', 'Product E', 1700, 17000, 9500, 7500],
        ['2023-06-01', 'Product A', 1400, 14000, 8000, 6000],
        ['2023-06-15', 'Product B', 2100, 21000, 11500, 9500],
      ],
      timeSeries: {
        timestamps: [
          '2023-01-10', '2023-01-20', '2023-02-05', '2023-02-15', '2023-03-01', '2023-03-15',
          '2023-04-01', '2023-04-15', '2023-05-01', '2023-05-15', '2023-06-01', '2023-06-15'
        ],
        values: [12000, 19000, 15000, 21000, 16000, 13000, 20000, 16000, 22000, 17000, 14000, 21000],
      },
      statistics: {
        values: [12000, 19000, 15000, 21000, 16000, 13000, 20000, 16000, 22000, 17000, 14000, 21000],
      },
    },
  },
];

import React, { useEffect, useRef, useState } from 'react';
import { Modal, Button, Input, Checkbox } from 'antd';
import type { InputRef } from 'antd';

interface FileModalProps {
    visible: boolean;
    onConfirm: (name: string, workflowType: string) => void;
    onCancel: () => void;
}

const FileModal: React.FC<FileModalProps> = ({ visible, onConfirm, onCancel }) => {
    const [name, setName] = useState('');
    const [isCustom, setIsCustom] = useState(false);
    const inputRef = useRef<InputRef>(null);

    useEffect(() => {
        if (visible) {
            setTimeout(() => {
                inputRef.current?.focus(); 
            }, 200);
        }
    }, [visible]);

    const handleConfirm = () => {
        onConfirm(name, isCustom ? 'custom' : 'general');
        setName('');
        setIsCustom(false);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setName(e.target.value);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleConfirm();
        }
    };

    return (
        <Modal
            title="Add Workflow"
            visible={visible}
            onOk={handleConfirm}
            onCancel={() => {
                setName('');
                onCancel();
            }}
            okText="Add"
            cancelText="Cancel"
        >
            <Input
                placeholder="Enter workflow name"
                ref={inputRef}
                value={name}
                onKeyDown={handleKeyDown} 
                onChange={handleInputChange}
                style={{ marginTop: 10 }}
            />
            <Checkbox
                checked={isCustom}
                onChange={(e) => setIsCustom(e.target.checked)}
                style={{ marginTop: 12 }}
            >
                Is Custom Workflow
            </Checkbox>
        </Modal>
    );
};

export default FileModal;

{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon src/index.js", "prod": "node src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.744.0", "@aws-sdk/client-secrets-manager": "^3.808.0", "@aws-sdk/s3-request-presigner": "^3.862.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.21.1", "fast-json-stable-stringify": "^2.1.0", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "mongoose": "^8.8.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.0.5", "nodemailer": "^7.0.3", "nodemon": "^3.1.7", "path": "^0.12.7", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "qrcode": "^1.5.4", "redis": "^4.7.0", "sequelize": "^6.37.5", "speakeasy": "^2.0.0"}}
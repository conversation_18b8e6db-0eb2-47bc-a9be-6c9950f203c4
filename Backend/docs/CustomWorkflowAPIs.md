
## API 1: Save/Update Custom Workflow Data

### Endpoint
`PUT /api/v1/custom-workflow/:id`

### Description
Updates custom workflow data in workflows, workflow components, and workflow structure tables. This is a PUT API that updates data each time it's called.

### Parameters
- **Path Parameters:**
  - `id` (required): Workflow ID

- **Request Body:**
```json
{
  "workflows": [
    {
      "name": "string",
      "description": "string",
      "folder_id": "number"
    }
  ],
  "workflowComponents": [
    {
      "component": "string",
      "settings": "object",
      "type": "string"
    }
  ],
  "workflowStructures": [
    {
      "hierarchy": "object"
    }
  ]
}
```

### Actions Performed
1. Validates workflow exists and is of type 'custom'
2. Updates workflow basic information in `workflows` table
3. Deletes existing workflow components and inserts new ones in `workflowComponents` table
4. Deletes existing workflow structures and inserts new ones in `workflowStructures` table
5. Uses database transactions for data consistency
6. Prepares payload for future AIML API integration

### Database Tables Used
- `workflows` - Updated with new workflow information
- `workflowcomponents` - Deleted and recreated with new component data
- `workflowstructures` - Deleted and recreated with new structure data

### Response
```json
{
  "message": "Custom workflow updated successfully",
  "data": {
    "workflowId": "number",
    "updated": true
  },
  "status": 200
}
```

---

## API 2: Get Detailed Workflow Data

### Endpoint
`GET /api/v1/custom-workflow/:id/details`

### Description
Returns detailed workflow data including information from workflows, workflow components, and workflow structure tables.

### Parameters
- **Path Parameters:**
  - `id` (required): Workflow ID

### Actions Performed
1. Validates workflow exists and is of type 'custom'
2. Fetches workflow basic information from `workflows` table
3. Fetches all related components from `workflowComponents` table
4. Fetches all related structures from `workflowStructures` table
5. Formats and returns comprehensive workflow data

### Database Tables Used
- `workflows` - Fetches basic workflow information
- `workflowcomponents` - Fetches all components for the workflow
- `workflowstructures` - Fetches structures for the workflow

---

## API 3: Execute Workflow with Node Data

### Endpoint
`POST /api/v1/custom-workflow/:workflowId/execute`

### Description
Executes a workflow with current node data, processes the data into a query format, and calls the Windmill API.

### Parameters
- **Path Parameters:**
  - `workflowId` (required): Workflow ID


### Actions Performed
1. Validates workflow exists and is of type 'custom'
2. Processes node data array and converts to query format
3. Combines all node data into a single formatted query
4. Prepares payload for Windmill API
5. Calls Windmill API with formatted payload
6. Returns execution results

### Database Tables Used
- `workflows` - Validates workflow existence and type

---


## API 4: Get User's Custom Workflows

### Endpoint
`GET /api/v1/custom-workflow/user-scripts`

### Description
Returns a list of custom workflows for the authenticated user. Supports search functionality and pagination.

### Parameters
- **Query Parameters:**
  - `searchedWorkflow` (optional): Search term to filter workflows by name
  - `limit` (optional): Number of results to return (default: 6)

### Actions Performed
1. Fetches custom workflows for the authenticated user
2. Applies search filter if `searchedWorkflow` parameter is provided
3. Returns first 5-6 workflows by default
4. Orders results by most recently updated
5. Returns only basic workflow information

### Database Tables Used
- `workflows` - Fetches user's custom workflows with optional name filtering

---

## API 5: Get Node Specific Data

### Endpoint
`GET /api/v1/custom-workflow/:workflowId/nodes`

### Description
Fetches all node data for a specific workflow from the workflow components table.

### Parameters
- **Path Parameters:**
  - `workflowId` (required): Workflow ID

### Actions Performed
1. Validates workflow exists and is of type 'custom'
2. Fetches all workflow components (nodes) for the specified workflow
3. Formats node data into an array of objects
4. Returns comprehensive node information

### Database Tables Used
- `workflows` - Validates workflow existence and type
- `workflowcomponents` - Fetches all nodes/components for the workflow

---

## API 6: Get Operations by Type

### Endpoint
`GET /api/v1/custom-workflow/operations`

### Description
Fetches operations dynamically based on operation type (general or custom) from the operations table.

### Parameters
- **Query Parameters:**
  - `operationType` (optional): Type of operations to fetch ('general' or 'custom', default: 'general')

### Actions Performed
1. Fetches operations from the operations table based on the specified type
2. Filters only active operations (isActive = true)
3. Orders results alphabetically by name
4. Returns formatted operation data

### Database Tables Used
- `operations` - Fetches operations filtered by type and active status



## Database Schema Requirements

### Operations Table Update
The `operations` table has been updated to include a `type` field:

```sql
ALTER TABLE operations ADD COLUMN operation_type VARCHAR(50) DEFAULT 'general';
```
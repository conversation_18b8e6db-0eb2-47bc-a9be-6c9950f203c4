// Test script to verify the blends API functionality
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';

const testCSVReading = async () => {
    console.log('Testing CSV reading functionality...');
    
    const csvPath = path.join(process.cwd(), 'uploads', 'blends_1.csv');
    console.log('CSV Path:', csvPath);
    
    if (!fs.existsSync(csvPath)) {
        console.error('❌ CSV file not found at:', csvPath);
        return;
    }
    
    console.log('✅ CSV file exists');
    
    const results = [];
    
    return new Promise((resolve, reject) => {
        fs.createReadStream(csvPath)
            .pipe(csv())
            .on('data', (data) => {
                // Transform the data to match the required format
                const transformedData = {
                    datetime: data.DateTime,
                    batch_id: data['Batch Id'],
                    L_color_value: parseFloat(data.L_color_value) || 0,
                    R_color_value: parseFloat(data.C_color_value) || 0, // Using C_color_value as R_color_value
                    C_color_value: parseFloat(data.H_color_value) || 0, // Using H_color_value as C_color_value
                    moist: parseFloat(data.Moist) || 0,
                    ffa: parseFloat(data.FFA) || 0,
                    iv: parseFloat(data.IV) || 0,
                    ph: parseFloat(data.PH) || 0,
                    fat: parseFloat(data.Fat) || 0,
                    cluster: data.Cluster_prev || 'Unknown',
                    quality: parseInt(data.quality) || 0
                };
                results.push(transformedData);
            })
            .on('end', () => {
                console.log(`✅ Successfully parsed ${results.length} records`);
                console.log('Sample record:', JSON.stringify(results[0], null, 2));
                
                // Test sequential with random colors selection
                const randomRecords = getSequentialWithRandomColors(results, 5);
                console.log(`✅ Sequential with random colors working - got ${randomRecords.length} records`);
                console.log('Sample sequential record with random colors:', JSON.stringify(randomRecords[0], null, 2));

                // Test sequential behavior - show first 3 records to verify sequence
                console.log('\n📊 Testing Sequential Behavior:');
                randomRecords.slice(0, 3).forEach((record, index) => {
                    console.log(`Record ${index + 1}: DateTime=${record.datetime}, BatchID=${record.batch_id}`);
                });
                
                resolve(results);
            })
            .on('error', (error) => {
                console.error('❌ Error reading CSV:', error);
                reject(error);
            });
    });
};

// Test sequential index
let testCurrentIndex = 0;

// Function to get sequential data with random color values (for testing)
const getSequentialWithRandomColors = (data, count = 5) => {
    if (data.length === 0) {
        return [];
    }

    const result = [];

    for (let i = 0; i < count; i++) {
        // Get current sequential row (wrap around if we reach the end)
        const sequentialRow = data[testCurrentIndex % data.length];

        // Get random indices for color values only
        const randomIndex1 = Math.floor(Math.random() * data.length);
        const randomIndex2 = Math.floor(Math.random() * data.length);
        const randomIndex3 = Math.floor(Math.random() * data.length);

        result.push({
            // Sequential data (exactly as in CSV)
            datetime: sequentialRow.datetime,
            batch_id: sequentialRow.batch_id,

            // Random color values from different rows
            L_color_value: data[randomIndex1].L_color_value,
            R_color_value: data[randomIndex2].R_color_value,
            C_color_value: data[randomIndex3].C_color_value,
        });

        // Move to next row for next iteration
        testCurrentIndex++;
    }

    return result;
};

// Run the test
testCSVReading()
    .then(() => {
        console.log('✅ All tests passed!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });

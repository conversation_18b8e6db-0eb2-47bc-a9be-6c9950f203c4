
import jwt from 'jsonwebtoken';
import { generateResponse } from "../utils/commonResponse.js";
import 'dotenv/config'

const generateToken = (userData) =>{
    return jwt.sign(userData,process.env.JWT_SECRET)

}

const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    // req.user = {
    //   first_name: '<PERSON>',
    //   last_name: '<PERSON><PERSON>',
    //   email: '<EMAIL>',
    //   id:1
    // }
    // next();
    const token = authHeader && authHeader.split(' ')[1];
  
    if (!token) {
        return generateResponse(res, 401, 'Access token required')
    }
  
    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
      if (err) {
        return generateResponse(res, 403, 'Invalid token')
      }
      req.user = user;
      next();
    });
  };

const skipSuperAdminActivity = (req, res, next) => {
  if (req.user?.isSuperAdmin) {
    return generateResponse(res, 200, 'Super admin activity not tracked');
  }
  next();
};

export {
    generateToken,
    authenticateToken,
    skipSuperAdminActivity
  }
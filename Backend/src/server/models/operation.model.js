import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";

const Operations = sequelize.define('operations', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
    },
    alias: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    icon: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true
    },
    user_id: {
        type: DataTypes.INTEGER,
        references: {
            model: 'users',
            key: 'id'
        },
        onDelete: 'CASCADE',
        allowNull: true,
    },
    operation_type: {
        type: DataTypes.ENUM('general', 'custom'),
        allowNull: false,
        defaultValue: 'custom',
    },
    input_type: {
        type: DataTypes.STRING(100),
        allowNull: true,
    },
    output_type: {
        type: DataTypes.STRING(100),
        allowNull: true,
    },
    config: {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: {},
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'operations',
    timestamps: false,
});

// Operations.hasMany(OtherModel, { foreignKey: 'operation_id' });
// OtherModel.belongsTo(Operations, { foreignKey: 'operation_id' });

export default Operations;

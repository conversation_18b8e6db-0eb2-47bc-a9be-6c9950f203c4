// const { DataTypes } = require('sequelize');
import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import WorkflowStructure from "./workflowStructure.model.js";
import WorkflowComponents from "./workflowComponents.model.js";
import Folder from "./folder.model.js";
import workflowFilters from "./workflowFilters.model.js";

const Workflow = sequelize.define('workflows', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    // tenant_id: {
    //     type: DataTypes.INTEGER,
    //     allowNull: false,
    //     references: {
    //         model: 'Tenants',
    //         key: 'id'
    //     }
    // },
    user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'Users',
            key: 'id'
        }
    },
    hide_for_s3: {
        type: DataTypes.BOOLEAN,
        defaultValue: false, 
        allowNull: false, 
    },
    folder_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'Folders',
          key: 'id',
        },
        onDelete: 'SET NULL', 
        onUpdate: 'CASCADE',
      },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    columns_order: {
        type: DataTypes.ARRAY(DataTypes.INTEGER), // Define as an array of integers
        defaultValue: [], // Default value is an empty array
    },
    systems_id: {
        type: DataTypes.ARRAY(DataTypes.STRING), // Changed to store system names (strings)
        allowNull: true,
        defaultValue: [], // Default to an empty array
    },
    filter_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'workflowFilters',
          key: 'id',
        },
        onDelete: 'SET NULL', 
        onUpdate: 'CASCADE',
      },
    custom_filter_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'workflowFilters',
          key: 'id',
        },
        onDelete: 'SET NULL', 
        onUpdate: 'CASCADE',
      },
    file_saved_path_for_aiml: {
        type: DataTypes.STRING,
        allowNull: true, 
    },
    workflow_type: {
        type: DataTypes.STRING,
        allowNull: true, // Allow null if not specified
        defaultValue: 'general', // Default value if not specified
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'workflows',
    timestamps: false,
});

// Workflow.hasMany(WorkflowStructure, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });
// Workflow.hasMany(WorkflowComponents, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });

// WorkflowStructure.belongsTo(Workflow, { foreignKey: 'workflow_id' });
// WorkflowComponents.belongsTo(Workflow, { foreignKey: 'workflow_id' });

Workflow.hasOne(WorkflowStructure, {
    foreignKey: 'workflow_id',
    as: 'workflowStructure',
});

Workflow.hasMany(WorkflowComponents, {
    foreignKey: 'workflow_id',
    as: 'workflowComponents',
});

Workflow.belongsTo(Folder, {
    foreignKey: 'folder_id',
    as: 'folder',
  });
Workflow.belongsTo(workflowFilters, {
    foreignKey: 'filter_id',
    as: 'filter',
  });
export default Workflow;

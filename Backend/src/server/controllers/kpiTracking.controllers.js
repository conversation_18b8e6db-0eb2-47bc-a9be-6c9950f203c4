import { generateResponse } from "../utils/commonResponse.js";
import KpiTrackingMongo from "../models/kpiTracking.mongo.model.js";
import Systems from "../models/systems.model.js";
import csv from 'csv-parser';
import fs from 'fs';
import Tenants from "../models/tenants.model.js";

const mapOperatorToMongo = (operator, value) => {
  // Helper function to parse values to numbers if possible
  const parseValue = (val) => {
    const num = parseFloat(val);
    return isNaN(num) ? val : num;
  };

  let parsedValue;
  if (Array.isArray(value)) {
    parsedValue = value.map((v) => parseValue(v));
  } else {
    parsedValue = parseValue(value);
  }

  switch (operator) {
    case "=":
      return parsedValue;
    case "!=":
      return { $ne: parsedValue };
    case "<":
      return { $lt: parsedValue };
    case "<=":
      return { $lte: parsedValue };
    case ">":
      return { $gt: parsedValue };
    case ">=":
      return { $gte: parsedValue };
    case "between":
      if (Array.isArray(parsedValue) && parsedValue.length === 2) {
        return { $gte: parsedValue[0], $lte: parsedValue[1] };
      }
      throw new Error("Between operator requires an array of two values");
    case "notBetween":
      if (Array.isArray(parsedValue) && parsedValue.length === 2) {
        return { $lt: parsedValue[0], $gt: parsedValue[1] };
      }
      throw new Error("notBetween operator requires an array of two values");
    default:
      return parsedValue;
  }
};

// For table data with pagination
export const getKpiTrackingData = async (req, res) => {
  let tenant_type = await Tenants.findOne({
    where: { id: req.user.tenant_id },
    attributes: ["tenant_type"],
  });
  console.log('tenant_type', tenant_type?.dataValues?.tenant_type)
  tenant_type = tenant_type?.dataValues?.tenant_type

  const tenantId = tenant_type == "demo" ? 1 : req.user.tenant_id;
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 10;
  const startDate = req.query.startDate;
  const endDate = req.query.endDate;
  const genericFilters = req.query.genericFilters
    ? JSON.parse(req.query.genericFilters)
    : null;

  try {
    const query = { tenantId: parseInt(tenantId) };

    // Date range filter
    if (startDate || endDate) {
      query.datetime = {};
      if (startDate) query.datetime.$gte = new Date(startDate);
      if (endDate) query.datetime.$lte = new Date(endDate);
    }

    // Generic filters
    if (genericFilters && genericFilters.length > 0) {
      genericFilters.forEach((filter) => {
        const { field, operator, value } = filter;
        if (field && operator && value !== undefined) {
          query[field] = mapOperatorToMongo(operator, value);
        }
      });
    }

    const skip = (page - 1) * pageSize;
    const limit = pageSize;

    const [data, count] = await Promise.all([
      KpiTrackingMongo.find(query)
        .sort({ datetime: -1 })
        .skip(skip)
        .limit(limit),
      KpiTrackingMongo.countDocuments(query),
    ]);
    // Extract unique system_ids from MongoDB data
    const systemIds = [
      ...new Set(data.map((d) => d.system_id).filter(Boolean)),
    ];

    // Fetch system names from PostgreSQL
    let systemNamesMap = {};
    if (systemIds.length > 0) {
      const systems = await Systems.findAll({
        where: { id: systemIds },
        attributes: ["id", "name"],
      });

      // Create a map of system_id -> system_name
      systemNamesMap = systems.reduce((acc, system) => {
        acc[system.id] = system.name;
        return acc;
      }, {});
    }
    let kpiData = [...data];
    const enrichedData = kpiData.map((record) => ({
      ...record.toObject(),
      name: systemNamesMap[record.system_id] || null, // Assign name or null if not found
    }));
    return generateResponse(
      res,
      200,
      "KPI tracking data fetched successfully",
      {
        data: enrichedData,
        total: count,
        page,
        pageSize,
      }
    );
  } catch (error) {
    console.error("Error fetching KPI tracking data:", error);
    return generateResponse(res, 500, "Internal server error");
  }
};

// For graph data with pre-calculated averages and statistics
export const getKpiTrackingGraphData = async (req, res) => {
  let tenant_type = await Tenants.findOne({
    where: { id: req.user.tenant_id },
    attributes: ["tenant_type"],
  });
  console.log('tenant_type', tenant_type?.dataValues?.tenant_type)
  tenant_type = tenant_type?.dataValues?.tenant_type

  const tenantId = tenant_type == "demo" ? 1 : req.user.tenant_id;
  const startDate = req.query.startDate;
  const endDate = req.query.endDate;
  const groupBy = req.query.groupBy || "daily";
  const genericFilters = req.query.genericFilters
    ? JSON.parse(req.query.genericFilters)
    : null;
  const systemConfigurations = req.query.systemConfigurations
    ? JSON.parse(req.query.systemConfigurations)
    : null;

  try {
    if (tenantId !== 1 && tenantId !== 2) {
      return generateResponse(res, 200, "No data available for this tenant", {
        data: [],
      });
    }

    // Select the correct field based on tenantId
    console.log('req.user.tenant_id', req.user.tenant_id)
    const measurementField = req.user.tenant_id == 1 || req.user.tenant_id == 6 ? "ph" : req.user.tenant_id == 3 ? req.query?.lchValue : "rerun_iodine";
    console.log('measurementField', measurementField)

    const query = { tenantId: parseInt(tenantId) };

    // Apply date filters
    if (startDate || endDate) {
      query.datetime = {};
      if (startDate) query.datetime.$gte = new Date(startDate);
      if (endDate) query.datetime.$lte = new Date(endDate);
    }

    // Apply generic filters
    if (genericFilters && genericFilters.length > 0) {
      genericFilters.forEach((filter) => {
        const { field, operator, value } = filter;
        if (field && operator && value !== undefined) {
          query[field] = mapOperatorToMongo(operator, value);
        }
      });
    }

    // Extract fieldRanges from systemConfigurations
    let allFieldRanges = {};
    if (systemConfigurations && Array.isArray(systemConfigurations)) {
      systemConfigurations.forEach(config => {
        if (config.fieldRanges) {
          // Merge all fieldRanges from different systems
          allFieldRanges = { ...allFieldRanges, ...config.fieldRanges };
        }
      });
    }

    if (req.user.tenant_id == 3) {
      const measurementField = req.query?.lchValue;
      if (measurementField) {
        allFieldRanges = {
          [measurementField]: { min: 6.00, max: 6.45 }
        };
      }
    }
    // Define group format based on selected granularity
    const groupByFormat =
      {
        daily: "%Y-%m-%d",
        weekly: "%Y-%V",
        monthly: "%Y-%m",
      }[groupBy] || "%Y-%m-%d";

    let aggregationPipeline = [

      //string dates were not filtering
      {
        $addFields: {
          datetime: {
            $cond: [
              { $eq: [{ $type: "$datetime" }, "string"] },
              { $toDate: "$datetime" },
              "$datetime",
            ],
          },
        },
      },
      {
        $match: {
          ...query,
          datetime: { $gte: new Date(startDate), $lte: new Date(endDate) },
        },
      },
      {
        $addFields: {
          dateGroup: {
            $dateToString: { format: groupByFormat, date: "$datetime" },
          },
          numericValue: { $toDouble: `$${measurementField}` }, // Ensure numeric conversion
        },
      },
    ];

    // Exclude null values for tenant 3 as null values were being calculated
    if (req.user.tenant_id == 3) {
      aggregationPipeline.push({
        $match: {
          [measurementField]: { $ne: null }
        }
      });
    }


    // Add percentage calculation logic if fieldRanges are provided
    let groupStage = {
      _id: "$dateGroup",
      mean_value: { $avg: "$numericValue" },
      std_dev_value: { $stdDevPop: "$numericValue" },
      count: { $sum: 1 },
      values: { $push: "$numericValue" }, // Store values for median calculation
    };

    // Add percentage calculations for each field in allFieldRanges
    if (Object.keys(allFieldRanges).length > 0) {
      
      Object.keys(allFieldRanges).forEach(fieldName => {
        const range = allFieldRanges[fieldName];
        const { min, max } = range;


        if (min !== undefined && max !== undefined) {
          // Check if field exists in the document and count records that fall within the range (good)
          groupStage[`${fieldName}_good_count`] = {
            $sum: {
              $cond: {
                if: {
                  $and: [
                    { $ne: [`$${fieldName}`, null] }, // Field exists and is not null
                    { $ne: [`$${fieldName}`, undefined] }, // Field is not undefined
                    { $gte: [{ $toDouble: `$${fieldName}` }, min] },
                    { $lte: [{ $toDouble: `$${fieldName}` }, max] }
                  ]
                },
                then: 1,
                else: 0
              }
            }
          };

          // Count records that fall outside the range (bad) but field exists
          groupStage[`${fieldName}_bad_count`] = {
            $sum: {
              $cond: {
                if: {
                  $and: [
                    { $ne: [`$${fieldName}`, null] }, // Field exists and is not null
                    { $ne: [`$${fieldName}`, undefined] }, // Field is not undefined
                    {
                      $or: [
                        { $lt: [{ $toDouble: `$${fieldName}` }, min] },
                        { $gt: [{ $toDouble: `$${fieldName}` }, max] }
                      ]
                    }
                  ]
                },
                then: 1,
                else: 0
              }
            }
          };

          // Count total records where field exists
          groupStage[`${fieldName}_total_count`] = {
            $sum: {
              $cond: {
                if: {
                  $and: [
                    { $ne: [`$${fieldName}`, null] },
                    { $ne: [`$${fieldName}`, undefined] }
                  ]
                },
                then: 1,
                else: 0
              }
            }
          };

          console.log(`@@@ Added aggregation fields for ${fieldName}:`, {
            good_count: `${fieldName}_good_count`,
            bad_count: `${fieldName}_bad_count`,
            total_count: `${fieldName}_total_count`
          });
        }
      });
    }

    aggregationPipeline.push({ $group: groupStage });

    // Add median calculation
    aggregationPipeline.push({
      $addFields: {
        // for myself as mongo 4.2 does not support $media
        median_value: {
          $function: {
            body: function(values) {
              if (!Array.isArray(values) || values.length === 0) return null;
              values = values.map(Number).sort((a, b) => a - b);
              const mid = Math.floor(values.length / 2);
              return values[mid];
            },
            args: ["$values"],
            lang: "js",
          },
        },

        // ------
        // median_value: {
        //   $let: {
        //     vars: {
        //       sortedValues: { $sortArray: { input: "$values", sortBy: 1 } },
        //     },
        //     in: {
        //       $cond: {
        //         if: { $gt: [{ $size: "$$sortedValues" }, 0] },
        //         then: {
        //           $arrayElemAt: [
        //             "$$sortedValues",
        //             { $floor: { $divide: [{ $size: "$$sortedValues" }, 2] } },
        //           ],
        //         },
        //         else: null,
        //       },
        //     },
        //   },
        // },
      },
    });

    // Add percentage calculations
    if (Object.keys(allFieldRanges).length > 0) {
      let addFieldsStage = {};
      
      Object.keys(allFieldRanges).forEach(fieldName => {
        addFieldsStage[`${fieldName}_good_percentage`] = {
          $cond: {
            if: { $gt: [`$${fieldName}_total_count`, 0] },
            then: {
              $multiply: [
                { $divide: [`$${fieldName}_good_count`, `$${fieldName}_total_count`] },
                100
              ]
            },
            else: 0
          }
        };

        addFieldsStage[`${fieldName}_bad_percentage`] = {
          $cond: {
            if: { $gt: [`$${fieldName}_total_count`, 0] },
            then: {
              $multiply: [
                { $divide: [`$${fieldName}_bad_count`, `$${fieldName}_total_count`] },
                100
              ]
            },
            else: 0
          }
        };

      });

      aggregationPipeline.push({ $addFields: addFieldsStage });
    }

    // Remove unnecessary fields and sort
    aggregationPipeline.push({ $project: { values: 0 } }); // Remove unnecessary array
    aggregationPipeline.push({ $sort: { _id: 1 } });

    if (groupBy == "weekly") aggregationPipeline.push({ $skip: 1 });

    console.log('aggregationPipeline', JSON.stringify(aggregationPipeline) )
    const results = await KpiTrackingMongo.aggregate(aggregationPipeline);
    

    const formattedResults = results.map((result, index) => {
      
      let formattedResult = {
        date: result._id,
        mean_value:
          result.mean_value !== null && result.mean_value !== undefined
            ? parseFloat(result.mean_value.toFixed(2))
            : null,
        median_value:
          result.median_value !== null && result.median_value !== undefined
            ? parseFloat(result.median_value.toFixed(2))
            : null,
        std_dev_value:
          result.std_dev_value !== null && result.std_dev_value !== undefined
            ? parseFloat(result.std_dev_value.toFixed(2))
            : null,
        count: result.count,
      };


      // Add percentage data if available
      if (Object.keys(allFieldRanges).length > 0) {
        
        Object.keys(allFieldRanges).forEach(fieldName => {
          const goodPercentage = result[`${fieldName}_good_percentage`];
          const badPercentage = result[`${fieldName}_bad_percentage`];
          
          
          if (goodPercentage !== undefined && goodPercentage !== null) {
            formattedResult.good_percentage = parseFloat(goodPercentage.toFixed(2));
          } else {
          }
          
          if (badPercentage !== undefined && badPercentage !== null) {
            formattedResult.bad_percentage = parseFloat(badPercentage.toFixed(2));
          } else {
          }
        });
      }

      return formattedResult;
    });


    return generateResponse(res, 200, "Graph data fetched successfully", {
      data: formattedResults,
    });
  } catch (error) {
    console.error("Error fetching graph data:", error);
    return generateResponse(res, 500, "Internal server error");
  }
};

// CSV Upload function for AI/ML engineers
export const uploadKpiCsv = async (req, res) => {
  const { tenant_id } = req.user;
  
  try {
    if (!req.file) {
      return generateResponse(res, 400, 'No file uploaded');
    }

    const filePath = req.file.path;
    const records = [];
    let headers = [];
    
    // Column mapping patterns (case-insensitive)
    const columnMappings = {
      // System identifier patterns
      system_id: ['system_id', 'systemid', 'system_identifier', 'system', 'systemname', 'system_name'],
      // DateTime patterns  
      datetime: ['datetime', 'date', 'timestamp', 'time', 'date_time'],
      // pH patterns
      ph: ['ph', 'p_h', 'ph_value', 'ph_level'],
      // Recipe number patterns
      recipe_no: ['recipe_no', 'recipeno', 'recipe_number', 'recipe_id', 'recipe'],
      // Vat number patterns
      vat_no: ['vat_no', 'vatno', 'vat_number', 'vat_id', 'vat'],
      // Sequence number patterns
      sequence_no: ['sequence_no', 'sequenceno', 'sequence_number', 'sequence_id', 'sequence', 'seq_no', 'seqno']
    };

    // Function to find matching column name
    const findColumnName = (headers, patterns) => {
      for (const header of headers) {
        for (const pattern of patterns) {
          if (header.toLowerCase().trim() === pattern.toLowerCase()) {
            return header;
          }
        }
      }
      return null;
    };

    // Parse CSV file
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('headers', (headerList) => {
          headers = headerList;
        })
        .on('data', (row) => {
          records.push(row);
        })
        .on('end', resolve)
        .on('error', reject);
    });

    // Clean up temp file
    fs.unlink(filePath, (err) => {
      if (err) console.error('Error removing temp file:', err);
    });

    if (records.length === 0) {
      return generateResponse(res, 400, 'CSV file is empty or invalid');
    }

    // Map column names to standard fields
    const mappedColumns = {};
    for (const [standardField, patterns] of Object.entries(columnMappings)) {
      const foundColumn = findColumnName(headers, patterns);
      if (foundColumn) {
        mappedColumns[standardField] = foundColumn;
      }
    }

    // Validate required fields
    const requiredFields = ['datetime'];
    const missingRequired = requiredFields.filter(field => !mappedColumns[field]);
    
    if (missingRequired.length > 0) {
      return generateResponse(res, 400, `Missing required columns: ${missingRequired.join(', ')}. Found columns: ${headers.join(', ')}`);
    }

    // Check if we have system identifier (either system_id or system_name)
    if (!mappedColumns.system_id) {
      return generateResponse(res, 400, `Missing system identifier column. Expected one of: ${columnMappings.system_id.join(', ')}. Found columns: ${headers.join(', ')}`);
    }

    // Get current max ID for generating new IDs
    const maxIdRecord = await KpiTrackingMongo.findOne().sort({ id: -1 }).limit(1);
    let currentId = maxIdRecord ? maxIdRecord.id : 0;

    // Fetch all systems for name-to-ID mapping
    const allSystems = await Systems.findAll({
      where: { tenant_id },
      attributes: ['id', 'name']
    });
    
    const systemNameToId = {};
    const systemIdToName = {};
    allSystems.forEach(system => {
      systemNameToId[system.name.toLowerCase()] = system.id;
      systemIdToName[system.id] = system.name;
    });

    // Process records
    const processedRecords = [];
    const errors = [];

    for (let i = 0; i < records.length; i++) {
      const row = records[i];
      const recordErrors = [];
      
      try {
        currentId++;
        const processedRow = {
          id: currentId,
          tenantId: tenant_id,
          datetime: null,
          systemId: null
        };

        // Process datetime (required)
        const dateValue = row[mappedColumns.datetime];
        if (!dateValue) {
          recordErrors.push('Missing datetime value');
        } else {
          const parsedDate = new Date(dateValue);
          if (isNaN(parsedDate.getTime())) {
            recordErrors.push(`Invalid datetime format: ${dateValue}`);
          } else {
            processedRow.datetime = parsedDate;
          }
        }

        // Process system_id or system_name
        const systemValue = row[mappedColumns.system_id];
        if (!systemValue) {
          recordErrors.push('Missing system identifier value');
        } else {
          // Check if it's a numeric ID
          const numericSystemId = parseInt(systemValue);
          if (!isNaN(numericSystemId) && systemIdToName[numericSystemId]) {
            processedRow.systemId = numericSystemId;
          } else {
            // Try to match by system name
            const systemId = systemNameToId[systemValue.toLowerCase()];
            if (systemId) {
              processedRow.systemId = systemId;
            } else {
              recordErrors.push(`System not found: ${systemValue}. Available systems: ${Object.keys(systemNameToId).join(', ')}`);
            }
          }
        }

        // Process optional fields
        if (mappedColumns.ph && row[mappedColumns.ph]) {
          const phValue = parseFloat(row[mappedColumns.ph]);
          if (!isNaN(phValue)) {
            processedRow.ph = phValue;
          }
        }

        if (mappedColumns.recipe_no && row[mappedColumns.recipe_no]) {
          const recipeValue = parseInt(row[mappedColumns.recipe_no]);
          if (!isNaN(recipeValue)) {
            processedRow.recipe_no = recipeValue;
          }
        }

        if (mappedColumns.vat_no && row[mappedColumns.vat_no]) {
          const vatValue = parseInt(row[mappedColumns.vat_no]);
          if (!isNaN(vatValue)) {
            processedRow.vat_no = vatValue;
          }
        }

        if (mappedColumns.sequence_no && row[mappedColumns.sequence_no]) {
          const seqValue = parseInt(row[mappedColumns.sequence_no]);
          if (!isNaN(seqValue)) {
            processedRow.sequence_no = seqValue;
          }
        }

        // Add any additional columns from CSV that aren't in our mapping
        for (const [csvColumn, value] of Object.entries(row)) {
          const isStandardField = Object.values(mappedColumns).includes(csvColumn);
          if (!isStandardField && value !== null && value !== undefined && value !== '') {
            // Try to convert to number if possible, otherwise keep as string
            const numValue = parseFloat(value);
            processedRow[csvColumn] = !isNaN(numValue) ? numValue : value;
          }
        }

        if (recordErrors.length === 0) {
          processedRecords.push(processedRow);
        } else {
          errors.push(`Row ${i + 2}: ${recordErrors.join(', ')}`);
        }

      } catch (error) {
        errors.push(`Row ${i + 2}: Processing error - ${error.message}`);
        currentId--; // Rollback ID increment on error
      }
    }

    // If there are errors, return them
    if (errors.length > 0) {
      const maxErrorsToShow = 10;
      const errorSummary = errors.slice(0, maxErrorsToShow);
      if (errors.length > maxErrorsToShow) {
        errorSummary.push(`... and ${errors.length - maxErrorsToShow} more errors`);
      }
      return generateResponse(res, 400, 'CSV processing errors', {
        errors: errorSummary,
        totalErrors: errors.length,
        successfulRecords: processedRecords.length,
        totalRecords: records.length
      });
    }

    // Insert records into MongoDB
    if (processedRecords.length > 0) {
      await KpiTrackingMongo.insertMany(processedRecords);
    }

    // Return success response
    return generateResponse(res, 200, 'CSV data uploaded successfully', {
      totalRecords: records.length,
      processedRecords: processedRecords.length,
      columnMappings: mappedColumns,
      sampleRecord: processedRecords[0] || null
    });

  } catch (error) {
    // Clean up temp file on error
    if (req.file && req.file.path) {
      fs.unlink(req.file.path, (err) => {
        if (err) console.error('Error removing temp file:', err);
      });
    }
    
    console.error('Error uploading KPI CSV:', error);
    return generateResponse(res, 500, 'Internal server error during CSV upload');
  }
};

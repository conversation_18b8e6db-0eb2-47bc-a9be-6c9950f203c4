import dotenv from 'dotenv';
dotenv.config();
import { 
  S3Client, 
  CreateMultipartUploadCommand,
  UploadPartCommand,
  CompleteMultipartUploadCommand,
  AbortMultipartUploadCommand
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { generateResponse } from '../utils/commonResponse.js';
import Tenants from '../models/tenants.model.js';

// AWS S3 Configuration
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'eu-central-1',
  // credentials: {
  //   accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  // },
});

const BUCKET_NAME = process.env.AWS_BUCKET_NAME;
const CHUNK_SIZE = 10 * 1024 * 1024; // 10MB chunks for optimal performance

/**
 * Initiate multipart upload
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const initiateMultipartUpload = async (req, res) => {
  try {
    const { fileName, fileType, fileSize } = req.body;
    
    if (!fileName || !fileType || !fileSize) {
      return generateResponse(res, 400, 'Missing required fields: fileName, fileType, fileSize');
    }

    const key = `${req.body.path}/${Date.now()}-${fileName}`;

    const tenantData = await Tenants.findOne({ where: { id: req.user.tenant_id } });
    const awsCredentials = tenantData?.dataValues?.aws_credentials;

    if(!awsCredentials || !awsCredentials.AWS_BUCKET_NAME) {
      return generateResponse(res, 400, 'AWS credentials not properly configured for tenant');
    }
    
    const createCommand = new CreateMultipartUploadCommand({
      Bucket: awsCredentials.AWS_BUCKET_NAME,
      Key: key,
      ContentType: fileType,
      Metadata: {
        'original-filename': fileName,
        'file-size': fileSize.toString(),
        'uploaded-by': req.user?.id?.toString() || 'unknown',
      }
    });

    const result = await s3Client.send(createCommand);
    
    // Calculate number of parts needed
    const totalParts = Math.ceil(fileSize / CHUNK_SIZE);
    
    const responseData = {
      uploadId: result.UploadId,
      key: key,
      totalParts: totalParts,
      chunkSize: CHUNK_SIZE
    };

    return generateResponse(res, 200, 'Multipart upload initiated successfully', responseData);
  } catch (error) {
    console.error('Error initiating upload:', error);
    return generateResponse(res, 500, 'Failed to initiate upload', null, error.message);
  }
};

/**
 * Get presigned URLs for upload parts
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getPresignedUrls = async (req, res) => {
  try {
    const { uploadId, key, partNumbers } = req.body;
    
    if (!uploadId || !key || !Array.isArray(partNumbers)) {
      return generateResponse(res, 400, 'Missing required fields: uploadId, key, partNumbers (array)');
    }

    const tenantData = await Tenants.findOne({ where: { id: req.user.tenant_id } });
    const awsCredentials = tenantData?.dataValues?.aws_credentials;

    if(!awsCredentials || !awsCredentials.AWS_BUCKET_NAME) {
      return generateResponse(res, 400, 'AWS credentials not properly configured for tenant');
    }

    const presignedUrls = await Promise.all(
      partNumbers.map(async (partNumber) => {
        const command = new UploadPartCommand({
          Bucket: awsCredentials.AWS_BUCKET_NAME,
          Key: key,
          PartNumber: partNumber,
          UploadId: uploadId,
        });

        const presignedUrl = await getSignedUrl(s3Client, command, {
          expiresIn: 3600, // 1 hour
        });

        return {
          partNumber,
          presignedUrl,
        };
      })
    );

    const responseData = { presignedUrls };
    return generateResponse(res, 200, 'Presigned URLs generated successfully', responseData);
  } catch (error) {
    console.error('Error generating presigned URLs:', error);
    return generateResponse(res, 500, 'Failed to generate presigned URLs', null, error.message);
  }
};

/**
 * Complete multipart upload
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const completeMultipartUpload = async (req, res) => {
  try {
    const { uploadId, key, parts } = req.body;
    
    if (!uploadId || !key || !Array.isArray(parts)) {
      return generateResponse(res, 400, 'Missing required fields: uploadId, key, parts (array)');
    }

    const tenantData = await Tenants.findOne({ where: { id: req.user.tenant_id } });
    const awsCredentials = tenantData?.dataValues?.aws_credentials;

    if(!awsCredentials || !awsCredentials.AWS_BUCKET_NAME) {
      return generateResponse(res, 400, 'AWS credentials not properly configured for tenant');
    }

    const completeCommand = new CompleteMultipartUploadCommand({
      Bucket: awsCredentials.AWS_BUCKET_NAME,
      Key: key,
      UploadId: uploadId,
      MultipartUpload: {
        Parts: parts.sort((a, b) => a.PartNumber - b.PartNumber),
      },
    });

    const result = await s3Client.send(completeCommand);
    
    const responseData = {
      success: true,
      location: result.Location,
      key: key,
      bucket: awsCredentials.AWS_BUCKET_NAME,
    };

    return generateResponse(res, 200, 'Upload completed successfully', responseData);
  } catch (error) {
    console.error('Error completing upload:', error);
    return generateResponse(res, 500, 'Failed to complete upload', null, error.message);
  }
};

/**
 * Abort multipart upload
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const abortMultipartUpload = async (req, res) => {
  try {
    const { uploadId, key } = req.body;
    
    if (!uploadId || !key) {
      return generateResponse(res, 400, 'Missing required fields: uploadId, key');
    }

    const tenantData = await Tenants.findOne({ where: { id: req.user.tenant_id } });
    const awsCredentials = tenantData?.dataValues?.aws_credentials;

    if(!awsCredentials || !awsCredentials.AWS_BUCKET_NAME) {
      return generateResponse(res, 400, 'AWS credentials not properly configured for tenant');
    }
    
    const abortCommand = new AbortMultipartUploadCommand({
      Bucket: awsCredentials.AWS_BUCKET_NAME,
      Key: key,
      UploadId: uploadId,
    });

    await s3Client.send(abortCommand);
    
    const responseData = { success: true };
    return generateResponse(res, 200, 'Upload aborted successfully', responseData);
  } catch (error) {
    console.error('Error aborting upload:', error);
    return generateResponse(res, 500, 'Failed to abort upload', null, error.message);
  }
};

import Workflow from '../models/workflow.model.js';
import WorkflowStructure from '../models/workflowStructure.model.js';
import WorkflowComponents from '../models/workflowComponents.model.js';
import Operations from '../models/operation.model.js';
import { sequelize } from "../database/dbConnection.js";
import { generateResponse } from "../utils/commonResponse.js";
import { Op } from 'sequelize';
import axios from 'axios';

/**
 * API 1: Save/Update custom workflow data
 * PUT /api/v1/custom-workflow/:id
 */
export const saveCustomWorkflow = async (req, res) => {
    const { id } = req.params;
    const { workflows, workflowComponents, workflowStructure } = req.body;
    const { id: userId } = req.user;

    const transaction = await sequelize.transaction();

    try {
        // Check if workflow exists and is custom type
        const existingWorkflow = await Workflow.findByPk(id);
        
        if (!existingWorkflow) {
            await transaction.rollback();
            return generateResponse(res, 404, 'Workflow not found');
        }

        if (existingWorkflow.workflow_type !== 'custom') {
            await transaction.rollback();
            return generateResponse(res, 400, 'Workflow is not a custom workflow');
        }

        // Update workflow data
        if (workflows && workflows.length > 0) {
            for (const workflowData of workflows) {
                await Workflow.update(
                    { ...workflowData, updated_at: new Date() },
                    { where: { id }, transaction }
                );
            }
        }

        // Update workflow components
        if (workflowComponents && workflowComponents.length > 0) {
            // Delete existing components
            await WorkflowComponents.destroy({
                where: { workflow_id: id },
                transaction
            });

            // Insert new components
            const componentsToInsert = workflowComponents.map(component => ({
                ...component,
                workflow_id: id,
                created_at: new Date(),
                updated_at: new Date()
            }));

            await WorkflowComponents.bulkCreate(componentsToInsert, { transaction });
        }

        // Update workflow structures
        if (workflowStructure && workflowStructure.length > 0) {
            // Delete existing structures
            await WorkflowStructure.destroy({
                where: { workflow_id: id },
                transaction
            });

            // Insert new structures
            const structuresToInsert = workflowStructure.map(structure => ({
                ...structure,
                workflow_id: id,
                created_at: new Date(),
                updated_at: new Date()
            }));

            console.log('structuresToInsert', structuresToInsert)
            await WorkflowStructure.bulkCreate(structuresToInsert, { transaction });
        }

        await transaction.commit();

        // TODO: Format payload for AIML API call
        // const aimlPayload = formatForAimlApi({ workflows, workflowComponents, workflowStructures });
        // await callAimlApi(aimlPayload);

        return generateResponse(res, 200, 'Custom workflow updated successfully', {
            workflowId: id,
            updated: true
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error updating custom workflow:', error);
        return generateResponse(res, 500, 'Failed to update custom workflow');
    }
};

/**
 * API 2: Get detailed workflow data
 * GET /api/v1/custom-workflow/:id/details
 */
export const getDetailedWorkflowData = async (req, res) => {
    const { id } = req.params;

    try {
        // Check if workflow exists and is custom type
        const workflow = await Workflow.findOne({
            where: { 
                id,
                workflow_type: 'custom'
            }
        });

        if (!workflow) {
            return generateResponse(res, 404, 'Custom workflow not found');
        }

        // Fetch related data from all tables
        const [workflowComponents, workflowStructures] = await Promise.all([
            WorkflowComponents.findAll({
                where: { workflow_id: id }
            }),
            WorkflowStructure.findAll({
                where: { workflow_id: id }
            })
        ]);

        // Format the response data
        const detailedWorkflowData = {
            workflow: {
                id: workflow.id,
                name: workflow.name,
                description: workflow.description,
                workflow_type: workflow.workflow_type,
                user_id: workflow.user_id,
                folder_id: workflow.folder_id,
                created_at: workflow.created_at,
                updated_at: workflow.updated_at
            },
            components: workflowComponents.map(component => ({
                id: component.id,
                component: component.component,
                settings: component.settings,
                type: component.type,
                created_at: component.created_at,
                updated_at: component.updated_at
            })),
            structures: workflowStructures.map(structure => ({
                id: structure.id,
                hierarchy: structure.hierarchy,
                created_at: structure.created_at,
                updated_at: structure.updated_at
            }))
        };

        return generateResponse(res, 200, 'Detailed workflow data fetched successfully', detailedWorkflowData);

    } catch (error) {
        console.error('Error fetching detailed workflow data:', error);
        return generateResponse(res, 500, 'Failed to fetch detailed workflow data');
    }
};

/**
 * API 3: Execute workflow with node data
 * POST /api/v1/custom-workflow/:workflowId/execute
 */
export const executeWorkflowWithNodeData = async (req, res) => {
    const { workflowId } = req.params;
    const { nodeId, nodeDataArray } = req.body;

    try {
        // Validate workflow exists and is custom type
        const workflow = await Workflow.findOne({
            where: { 
                id: workflowId,
                workflow_type: 'custom'
            }
        });

        if (!workflow) {
            return generateResponse(res, 404, 'Custom workflow not found');
        }

        // Process node data and format into a single query
        let formattedQuery = '';
        if (nodeDataArray && nodeDataArray.length > 0) {
            // Combine all node data into a single query
            formattedQuery = nodeDataArray.map(nodeData => {
                // Process each node's data and convert to query format
                return processNodeDataToQuery(nodeData);
            }).join(' ');
        }

        // Format payload for Windmill API
        // const windmillPayload = {
        //     workflow_id: workflowId,
        //     node_id: nodeId,
        //     query: formattedQuery,
        //     node_data: nodeDataArray,
        //     timestamp: new Date().toISOString()
        // };

        // // Call Windmill API
        // let windmillResponse;
        // if (process.env.WINDMILL_API_URL) {
        //     windmillResponse = await axios.post(
        //         `${process.env.WINDMILL_API_URL}/execute`,
        //         windmillPayload,
        //         {
        //             headers: {
        //                 'Content-Type': 'application/json',
        //                 'Authorization': `Bearer ${process.env.WINDMILL_API_TOKEN}`
        //             }
        //         }
        //     );
        // } else {
        //     // Mock response for development
        //     windmillResponse = {
        //         data: {
        //             success: true,
        //             result: 'Mock execution result',
        //             executionId: 'mock-execution-id'
        //         }
        //     };
        // }

        return generateResponse(res, 200, 'Workflow executed successfully', {
            workflowId,
            nodeId,
            // executionResult: windmillResponse.data,
            processedQuery: formattedQuery
        });

    } catch (error) {
        console.error('Error executing workflow:', error);
        return generateResponse(res, 500, 'Failed to execute workflow');
    }
};

/**
 * Helper function to process node data into query format
 */
const processNodeDataToQuery = (nodeData) => {
    // This is a placeholder implementation
    // You'll need to implement the actual logic based on your node data structure
    if (nodeData.type === 'filter') {
        return `WHERE ${nodeData.field} ${nodeData.operator} '${nodeData.value}'`;
    } else if (nodeData.type === 'select') {
        return `SELECT ${nodeData.fields.join(', ')}`;
    } else if (nodeData.type === 'join') {
        return `JOIN ${nodeData.table} ON ${nodeData.condition}`;
    }
    return '';
};

/**
 * API 4: Get user's custom workflows with search functionality
 * GET /api/v1/custom-workflow/user-scripts
 */
export const getUserCustomWorkflows = async (req, res) => {
    const { id: userId } = req.user;
    const { searchedWorkflow, limit = 6 } = req.query;

    try {
        let whereCondition = {
            user_id: userId,
            workflow_type: 'custom'
        };

        // Add search condition if searchedWorkflow is provided
        if (searchedWorkflow) {
            whereCondition.name = {
                [Op.iLike]: `%${searchedWorkflow}%`
            };
        }

        const workflows = await Workflow.findAll({
            where: whereCondition,
            // attributes: [
            //     'id', 'name', 'description', 'workflow_type', 
            //     'created_at', 'updated_at', 'folder_id'
            // ],
            order: [['updated_at', 'DESC']],
            limit: parseInt(limit)
        });

        const formattedWorkflows = workflows.map(workflow => ({
            id: workflow.id,
            name: workflow.name,
            description: workflow.description,
            workflow_type: workflow.workflow_type,
            folder_id: workflow.folder_id,
            created_at: workflow.created_at,
            updated_at: workflow.updated_at
        }));

        return generateResponse(res, 200, 'User custom workflows fetched successfully', {
            workflows: formattedWorkflows,
            total: formattedWorkflows.length,
            searchTerm: searchedWorkflow || null
        });

    } catch (error) {
        console.error('Error fetching user custom workflows:', error);
        return generateResponse(res, 500, 'Failed to fetch user custom workflows');
    }
};

/**
 * API 5: Get node specific data for a workflow
 * GET /api/v1/custom-workflow/:workflowId/nodes
 */
export const getWorkflowNodeData = async (req, res) => {
    const { workflowId } = req.params;

    try {
        // Validate workflow exists and is custom type
        const workflow = await Workflow.findOne({
            where: {
                id: workflowId,
                workflow_type: 'custom'
            }
        });

        if (!workflow) {
            return generateResponse(res, 404, 'Custom workflow not found');
        }

        // Fetch all workflow components (nodes) for this workflow
        const workflowComponents = await WorkflowComponents.findAll({
            where: { workflow_id: workflowId },
            order: [['created_at', 'ASC']]
        });

        // Format the node data into an array of objects
        const nodeData = workflowComponents.map(component => ({
            nodeId: component.id,
            component: component.component,
            type: component.type,
            settings: component.settings,
            created_at: component.created_at,
            updated_at: component.updated_at
        }));

        return generateResponse(res, 200, 'Workflow node data fetched successfully', {
            workflowId,
            nodes: nodeData,
            totalNodes: nodeData.length
        });

    } catch (error) {
        console.error('Error fetching workflow node data:', error);
        return generateResponse(res, 500, 'Failed to fetch workflow node data');
    }
};
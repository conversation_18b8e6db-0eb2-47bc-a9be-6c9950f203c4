import { Router } from "express";
import { 
  initiateMultipartUpload,
  getPresignedUrls,
  completeMultipartUpload,
  abortMultipartUpload,
} from "../controllers/multipartUpload.controllers.js";
import { authenticateToken } from '../middlewares/jwt.js';

const router = Router();

// Multipart upload routes
router.route('/initiate').post(authenticateToken, initiateMultipartUpload);
router.route('/parts').post(authenticateToken, getPresignedUrls);
router.route('/complete').post(authenticateToken, completeMultipartUpload);
router.route('/abort').post(authenticateToken, abortMultipartUpload);

export default router;

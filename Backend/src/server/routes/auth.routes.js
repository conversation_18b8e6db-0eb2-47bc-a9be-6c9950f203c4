import { Router } from "express";
import {authenticateToken} from "../middlewares/jwt.js"
const router = Router()

// ********Import the controllers ******

import {
  loginUser,
  updateUserOnboardingStatus,
  updateUserSelectedSystems,
  usersList,
  setup2FA,
  verify2FA,
  disable2FA,
  get2FAStatus,
  verifyLoginWith2FA,
  encryptPassword,
  updateFirstLoginStatus,
  forgotPassword,
  verifyForgotPasswordOtp,
  changePassword,
  getTenantDetails
} from "../controllers/user.controllers.js";
import { sendOtp ,verifyEmailOtp} from "../controllers/auth.controller.js";


// ********Define path of controllers ******

router.route('/login').post(loginUser)
router.route('/login/verify-2fa').post(verifyLoginWith2FA)
router.route('/:userId/status').patch(authenticateToken,updateUserOnboardingStatus)
router.route('/:id/update-selected-systems').patch(authenticateToken,updateUserSelectedSystems)
router.route('/list').get(authenticateToken, usersList)
router.route('/send-otp').post(sendOtp);
router.route('/verify-otp').post(verifyEmailOtp);
// router.route('/register').post(upload.single('avtar'),registerUser)

// 2FA routes
router.route('/2fa/setup').get(authenticateToken, setup2FA)
router.route('/2fa/verify').post(authenticateToken, verify2FA)
router.route('/2fa/disable').post(authenticateToken, disable2FA)
router.route('/2fa/status').get(authenticateToken, get2FAStatus)
router.route('/update-first-login').post(authenticateToken, updateFirstLoginStatus)
router.route('/forget-password').post(forgotPassword)
router.route('/verify-forget-password-otp').post(verifyForgotPasswordOtp)
// Password encryption route
router.route('/encrypt-password').post(encryptPassword)
router.route('/change-password').patch(changePassword)

router.route('/tenant-details').get(authenticateToken, getTenantDetails)

export default router

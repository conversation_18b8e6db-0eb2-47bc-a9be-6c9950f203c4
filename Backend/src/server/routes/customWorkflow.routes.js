import express from 'express';
import { 
    saveCustomWorkflow, 
    getDetailedWorkflowData, 
    executeWorkflowWithNodeData, 
    getUserCustomWorkflows, 
    getWorkflowNodeData, 
} from '../controllers/customWorkflow.controllers.js';
import { authenticateToken } from '../middlewares/jwt.js';

const router = express.Router();

router.put('/:id', authenticateToken, saveCustomWorkflow);

router.get('/:id/details', authenticateToken, getDetailedWorkflowData);

router.post('/:workflowId/execute', authenticateToken, executeWorkflowWithNodeData);

router.get('/listings', authenticateToken, getUserCustomWorkflows);

router.get('/:workflowId/workflow-nodes', authenticateToken, getWorkflowNodeData);

export default router;

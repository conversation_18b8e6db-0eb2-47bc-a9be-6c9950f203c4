import { Router } from "express";
import { authenticateToken } from "../middlewares/jwt.js";
import {
    createSystemMeta,
    getSystemMeta,
    updateSystemMeta,
    deleteSystemMeta,
    markBatchesAsGolden,
    getGoldenBatches
} from "../controllers/systemMeta.controller.js";

const router = Router();

// Create system meta data
router.route('/').post(createSystemMeta);

// Get system meta data (with optional query parameters)
router.route('/').get(getSystemMeta);

// Get system meta data by ID
router.route('/:id').get(getSystemMeta);

// Update system meta data by ID
router.route('/:id').put(updateSystemMeta);

// Delete system meta data by ID
router.route('/:id').delete(authenticateToken, deleteSystemMeta);

// Mark batches as golden
router.route('/mark-golden').post(authenticateToken, markBatchesAsGolden);

// Get Marked golden batches
router.route('/golden-batches').post(authenticateToken, getGoldenBatches);

export default router;

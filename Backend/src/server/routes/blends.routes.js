import express from 'express';
import { 
    getRandomBlendData, 
    getAllBlendData, 
    refreshBlendData 
} from '../controllers/blends.controller.js';

const router = express.Router();

// Route to get random blend data
router.get('/random', getRandomBlendData);

// Route to get all blend data with pagination
router.get('/all', getAllBlendData);

// Route to refresh blend data cache
router.post('/refresh', refreshBlendData);

export default router;
